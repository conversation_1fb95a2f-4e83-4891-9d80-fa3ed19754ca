name: released image to DockerHub
on:
  release:
    types:
      - published
jobs:
  publish-image-to-dockerhub:
    name: publish to DockerHub
    permissions:
      id-token: write # To be able to get OIDC ID token to sign images.
    strategy:
      matrix:
        target:
          - karmada-controller-manager
          - karmada-scheduler
          - karmada-descheduler
          - karmada-webhook
          - karmada-agent
          - karmada-scheduler-estimator
          - karmada-interpreter-webhook-example
          - karmada-aggregated-apiserver
          - karmada-search
          - karmada-operator
          - karmada-metrics-adapter
    runs-on: ubuntu-22.04
    steps:
      - name: checkout code
        uses: actions/checkout@v3
        with:
          # fetch-depth:
          # 0 indicates all history for all branches and tags.
          # for `git describe --tags` in Makefile.
          fetch-depth: 0
      - name: install Go
        uses: actions/setup-go@v3
        with:
          go-version: 1.20.6
      - name: Install Cosign
        uses: sigstore/cosign-installer@v3.0.3
        with:
          cosign-release: 'v1.13.1'
      - name: install QEMU
        uses: docker/setup-qemu-action@v2
      - name: install Buildx
        uses: docker/setup-buildx-action@v2
      - name: login to DockerHub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USER_NAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
      - name: build and publish images
        env:
          REGISTRY: karmada
          VERSION: ${{ github.ref_name }}
          COSIGN_EXPERIMENTAL: 1
          SIGN_IMAGE: 1
        run: make mp-image-${{ matrix.target }}
