apiVersion: apps/v1
kind: Deployment
metadata:
  name: karmada-agent
  namespace: karmada-system
  labels:
    app: karmada-agent
spec:
  replicas: 2
  selector:
    matchLabels:
      app: karmada-agent
  template:
    metadata:
      labels:
        app: karmada-agent
    spec:
      serviceAccountName: karmada-agent-sa
      tolerations:
        - key: node-role.kubernetes.io/master
          operator: Exists
      containers:
        - name: karmada-agent
          image: docker.io/karmada/karmada-agent:latest
          imagePullPolicy: {{image_pull_policy}}
          command:
            - /bin/karmada-agent
            - --karmada-kubeconfig=/etc/kubeconfig/karmada-kubeconfig
            - --karmada-context={{karmada_context}}
            - --cluster-name={{member_cluster_name}}
            - --cluster-api-endpoint={{member_cluster_api_endpoint}}
            - --cluster-status-update-frequency=10s
            - --bind-address=0.0.0.0
            - --secure-port=10357
            - --feature-gates=CustomizedClusterResourceModeling=true
            - --v=4
          livenessProbe:
            httpGet:
              path: /healthz
              port: 10357
              scheme: HTTP
            failureThreshold: 3
            initialDelaySeconds: 15
            periodSeconds: 15
            timeoutSeconds: 5
          volumeMounts:
            - name: kubeconfig
              mountPath: /etc/kubeconfig
      volumes:
        - name: kubeconfig
          secret:
            secretName: karmada-kubeconfig
