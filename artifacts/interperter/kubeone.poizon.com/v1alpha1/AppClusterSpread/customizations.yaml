apiVersion: config.karmada.io/v1alpha1
kind: ResourceInterpreterCustomization
metadata:
  name: kubeone-appclusterspreads
spec:
  target:
    apiVersion: kubeone.poizon.com/v1alpha1
    kind: AppClusterSpread
  customizations:
    replicaResource:
      luaScript: >
        local kube = require("kube")
        function GetReplicas(obj)
          local subsets = obj.spec.subsets or {}
          local cnt = #subsets
          if (cnt == 0 or subsets[1].name ~= 'default')
          then
            return 0, nil
          else
            return subsets[1].maxReplicas, nil
          end
        end
    replicaRevision:
      luaScript: >
        function ReviseReplica(obj, desiredReplica)
          local subsets = obj.spec.subsets or {}
          local cnt = #subsets
          if (cnt > 0 and subsets[1].name == 'default')
          then
            obj.spec.subsets[1].maxReplicas = desiredReplica
          end
          return obj
        end
    statusAggregation:
      luaScript: >
        function AggregateStatus(desiredObj, statusItems)
          local mergedObj = {
              replicas = 0,
              subsetStatuses = {}
          }

          local names = {}
          for _, obj in ipairs(statusItems) do
              local objStatus = obj.status or {replicas = 0, subsetStatuses = {}}
              if objStatus.replicas > 0 then
                 mergedObj.replicas = mergedObj.replicas + objStatus.replicas
              end 
              for _, subset in ipairs(objStatus.subsetStatuses) do
                  local cur = names[subset.name]
                  if cur then
                      if (subset.replicas > 0) then
                          cur.replicas = cur.replicas + subset.replicas
                      end
                      if (subset.missingReplicas > 0) then
                          cur.replicas = cur.missingReplicas + subset.missingReplicas
                      end                          
                      names[subset.name] = cur
                  else
                      names[subset.name] = subset
                  end
              end
          end
          for key, value in pairs(names) do
              table.insert(mergedObj.subsetStatuses, value)
          end
          desiredObj.status = mergedObj
          return desiredObj
        end
    statusReflection:
      luaScript: >
        function ReflectStatus (observedObj)
          status = {}
          if observedObj == nil or observedObj.status == nil then 
            return status
          end
          return observedObj.status
        end
