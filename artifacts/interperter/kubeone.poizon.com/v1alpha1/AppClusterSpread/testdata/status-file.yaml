
applied: true
clusterName: member1
health: Healthy
status:
  replicas: 7
  subsetStatuses:
    - missingReplicas: 1
      name: default
      replicas: 2
    - missingReplicas: -1
      name: elastic-resource
      replicas: 5
---
applied: true
clusterName: member3
health: Healthy
status:
  replicas: 3
  subsetStatuses:
    - missingReplicas: 0
      name: default
      replicas: 1
    - missingReplicas: -1
      name: elastic-resource
      replicas: 2
