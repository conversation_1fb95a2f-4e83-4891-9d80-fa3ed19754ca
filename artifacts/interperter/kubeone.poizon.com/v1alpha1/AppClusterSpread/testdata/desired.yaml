apiVersion: kubeone.poizon.com/v1alpha1
kind: AppClusterSpread
metadata:
  labels:
    app-cluster: container-kubeone-porter-csprd-lst
    apps.dewu.com/kubefed-propagation-policy: multi-kubeone-test-default
  name: container-kubeone-porter-csprd-lst
  namespace: container-kubeone-porter
spec:
  subsets:
    - maxReplicas: 2
      name: default
    - maxReplicas: -1
      name: elastic-resource
      nodeSelector:
        dewu.com/resource-pool: autoscaler
status:
  replicas: 10
  subsetStatuses:
    - missingReplicas: 1
      name: default
      replicas: 3
    - missingReplicas: -1
      name: elastic-resource
      replicas: 7