#!/usr/bin/env bash

set -o errexit
set -o nounset

CURRENT_DIR=$(dirname "${BASH_SOURCE[0]}")
source "${CURRENT_DIR}/config-env.sh"
config_env "$@"

function installCRDs() {
    local context_name=$1
    local crd_path=$2

    kubectl --context="${context_name}" apply -k "${crd_path}"/_crds
}

TEMP_PATH_CRDS=$(mktemp -d)
trap '{ rm -rf ${TEMP_PATH_CRDS}; }' EXIT
cp -rf "${REPO_ROOT}"/charts/karmada/_crds "${TEMP_PATH_CRDS}"
util::fill_cabundle "${ROOT_CA_FILE}" "${TEMP_PATH_CRDS}/_crds/patches/webhook_in_resourcebindings.yaml"
util::fill_cabundle "${ROOT_CA_FILE}" "${TEMP_PATH_CRDS}/_crds/patches/webhook_in_clusterresourcebindings.yaml"
installCRDs "${KARMADA_CLUSTER_NAME}" "${TEMP_PATH_CRDS}"
