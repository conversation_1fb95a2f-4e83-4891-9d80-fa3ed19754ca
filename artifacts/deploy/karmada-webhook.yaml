apiVersion: apps/v1
kind: Deployment
metadata:
  name: karmada-webhook
  namespace: karmada-system
  labels:
    app: karmada-webhook
spec:
  replicas: 2
  selector:
    matchLabels:
      app: karmada-webhook
  template:
    metadata:
      labels:
        app: karmada-webhook
    spec:
      automountServiceAccountToken: false
      tolerations:
        - key: node-role.kubernetes.io/master
          operator: Exists
      containers:
        - name: karmada-webhook
          image: docker.io/karmada/karmada-webhook:latest
          imagePullPolicy: IfNotPresent
          command:
            - /bin/karmada-webhook
            - --kubeconfig=/etc/kubeconfig
            - --bind-address=0.0.0.0
            - --default-not-ready-toleration-seconds=30
            - --default-unreachable-toleration-seconds=30
            - --secure-port=8443
            - --cert-dir=/var/serving-cert
            - --v=4
          ports:
            - containerPort: 8443
          volumeMounts:
            - name: kubeconfig
              subPath: kubeconfig
              mountPath: /etc/kubeconfig
            - name: cert
              mountPath: /var/serving-cert
              readOnly: true
          readinessProbe:
            httpGet:
              path: /readyz
              port: 8443
              scheme: HTTPS
      volumes:
        - name: kubeconfig
          secret:
            secretName: kubeconfig
        - name: cert
          secret:
            secretName: webhook-cert
---
apiVersion: v1
kind: Service
metadata:
  name: karmada-webhook
  namespace: karmada-system
spec:
  selector:
    app: karmada-webhook
  ports:
    - port: 443
      targetPort: 8443
