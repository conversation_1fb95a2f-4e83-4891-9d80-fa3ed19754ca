apiVersion: apps/v1
kind: Deployment
metadata:
  name: karmada-kube-controller-manager
  namespace: karmada-system
  labels:
    app: kube-controller-manager
spec:
  replicas: 1
  selector:
    matchLabels:
      app: kube-controller-manager
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 1
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: kube-controller-manager
    spec:
      automountServiceAccountToken: false
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            - labelSelector:
                matchExpressions:
                  - key: app
                    operator: In
                    values:
                      - kube-controller-manager
              topologyKey: kubernetes.io/hostname
      containers:
        - command:
            - kube-controller-manager
            - --allocate-node-cidrs=true
            - --authentication-kubeconfig=/etc/kubeconfig
            - --authorization-kubeconfig=/etc/kubeconfig
            - --bind-address=0.0.0.0
            - --client-ca-file=/etc/karmada/pki/ca.crt
            - --cluster-cidr=**********/16
            - --cluster-name=karmada
            - --cluster-signing-cert-file=/etc/karmada/pki/ca.crt
            - --cluster-signing-key-file=/etc/karmada/pki/ca.key
            - --controllers=namespace,garbagecollector,serviceaccount-token,ttl-after-finished,bootstrapsigner,tokencleaner,csrapproving,csrcleaner,csrsigning,clusterrole-aggregation
            - --kubeconfig=/etc/kubeconfig
            - --leader-elect=true
            - --node-cidr-mask-size=24
            - --root-ca-file=/etc/karmada/pki/ca.crt
            - --service-account-private-key-file=/etc/karmada/pki/karmada.key
            - --service-cluster-ip-range=*********/12
            - --use-service-account-credentials=true
            - --v=4
          image: registry.k8s.io/kube-controller-manager:v1.25.4
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 8
            httpGet:
              path: /healthz
              port: 10257
              scheme: HTTPS
            initialDelaySeconds: 10
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 15
          name: kube-controller-manager
          resources:
            requests:
              cpu: 200m
          volumeMounts:
            - mountPath: /etc/karmada/pki
              name: karmada-certs
              readOnly: true
            - mountPath: /etc/kubeconfig
              subPath: kubeconfig
              name: kubeconfig
      priorityClassName: system-node-critical
      volumes:
        - name: karmada-certs
          secret:
            secretName: karmada-cert-secret
        - name: kubeconfig
          secret:
            secretName: kubeconfig
