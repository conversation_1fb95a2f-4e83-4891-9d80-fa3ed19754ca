apiVersion: apps/v1
kind: Deployment
metadata:
  name: karmada-apiserver
  namespace: karmada-system
  labels:
    app: karmada-apiserver
spec:
  replicas: 1
  selector:
    matchLabels:
      app: karmada-apiserver
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 1
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: karmada-apiserver
    spec:
      automountServiceAccountToken: false
      affinity:
        podAntiAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            - labelSelector:
                matchExpressions:
                  - key: app
                    operator: In
                    values:
                      - karmada-apiserver
              topologyKey: kubernetes.io/hostname
      containers:
        - command:
            - kube-apiserver
            - --allow-privileged=true
            - --authorization-mode=Node,RBAC
            - --client-ca-file=/etc/karmada/pki/ca.crt
            - --enable-bootstrap-token-auth=true
            - --etcd-cafile=/etc/karmada/pki/etcd-ca.crt
            - --etcd-certfile=/etc/karmada/pki/etcd-client.crt
            - --etcd-keyfile=/etc/karmada/pki/etcd-client.key
            - --etcd-servers=https://etcd-client.karmada-system.svc.cluster.local:2379
            - --bind-address=0.0.0.0
            - --kubelet-client-certificate=/etc/karmada/pki/karmada.crt
            - --kubelet-client-key=/etc/karmada/pki/karmada.key
            - --kubelet-preferred-address-types=InternalIP,ExternalIP,Hostname
            - --disable-admission-plugins=StorageObjectInUseProtection,ServiceAccount
            - --runtime-config=
            - --secure-port=5443
            - --service-account-issuer=https://kubernetes.default.svc.cluster.local
            - --service-account-key-file=/etc/karmada/pki/karmada.key
            - --service-account-signing-key-file=/etc/karmada/pki/karmada.key
            - --service-cluster-ip-range=*********/12
            - --proxy-client-cert-file=/etc/karmada/pki/front-proxy-client.crt
            - --proxy-client-key-file=/etc/karmada/pki/front-proxy-client.key
            - --requestheader-allowed-names=front-proxy-client
            - --requestheader-client-ca-file=/etc/karmada/pki/front-proxy-ca.crt
            - --requestheader-extra-headers-prefix=X-Remote-Extra-
            - --requestheader-group-headers=X-Remote-Group
            - --requestheader-username-headers=X-Remote-User
            - --tls-cert-file=/etc/karmada/pki/apiserver.crt
            - --tls-private-key-file=/etc/karmada/pki/apiserver.key
          name: karmada-apiserver
          image: registry.k8s.io/kube-apiserver:v1.25.4
          imagePullPolicy: IfNotPresent
          livenessProbe:
            failureThreshold: 8
            httpGet:
              path: /livez
              port: 5443
              scheme: HTTPS
            initialDelaySeconds: 10
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 15
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /readyz
              port: 5443
              scheme: HTTPS
            periodSeconds: 1
            successThreshold: 1
            timeoutSeconds: 15
          resources:
            requests:
              cpu: 250m
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
            - mountPath: /etc/karmada/pki
              name: karmada-certs
              readOnly: true
      dnsPolicy: ClusterFirstWithHostNet
      enableServiceLinks: true
      hostNetwork: true
      preemptionPolicy: PreemptLowerPriority
      priority: 2000001000
      priorityClassName: system-node-critical
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
      tolerations:
        - effect: NoExecute
          operator: Exists
      volumes:
        - name: karmada-certs
          secret:
            secretName: karmada-cert-secret
---
apiVersion: v1
kind: Service
metadata:
  name: karmada-apiserver
  namespace: karmada-system
  labels:
    app: karmada-apiserver
spec:
  ports:
    - name: karmada-apiserver-kubectl
      port: 5443
      protocol: TCP
      targetPort: 5443
  selector:
    app: karmada-apiserver
  type: {{service_type}}
