apiVersion: apps/v1
kind: Deployment
metadata:
  name: karmada-scheduler-estimator-{{member_cluster_name}}
  namespace: karmada-system
  labels:
    cluster: {{member_cluster_name}}
spec:
  replicas: 2
  selector:
    matchLabels:
      app: karmada-scheduler-estimator-{{member_cluster_name}}
  template:
    metadata:
      labels:
        app: karmada-scheduler-estimator-{{member_cluster_name}}
    spec:
      automountServiceAccountToken: false
      tolerations:
        - key: node-role.kubernetes.io/master
          operator: Exists
      containers:
        - name: karmada-scheduler-estimator
          image: docker.io/karmada/karmada-scheduler-estimator:latest
          imagePullPolicy: IfNotPresent
          command:
            - /bin/karmada-scheduler-estimator
            - --kubeconfig=/etc/{{member_cluster_name}}-kubeconfig
            - --cluster-name={{member_cluster_name}}
          livenessProbe:
            httpGet:
              path: /healthz
              port: 10351
              scheme: HTTP
            failureThreshold: 3
            initialDelaySeconds: 15
            periodSeconds: 15
            timeoutSeconds: 5
          volumeMounts:
            - name: member-kubeconfig
              subPath: {{member_cluster_name}}-kubeconfig
              mountPath: /etc/{{member_cluster_name}}-kubeconfig
      volumes:
        - name: member-kubeconfig
          secret:
            secretName: {{member_cluster_name}}-kubeconfig
---
apiVersion: v1
kind: Service
metadata:
  name: karmada-scheduler-estimator-{{member_cluster_name}}
  namespace: karmada-system
  labels:
    cluster: {{member_cluster_name}}
spec:
  selector:
    app: karmada-scheduler-estimator-{{member_cluster_name}}
  ports:
    - protocol: TCP
      port: 10352
      targetPort: 10352
