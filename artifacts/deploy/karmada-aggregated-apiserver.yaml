---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: karmada-aggregated-apiserver
  namespace: karmada-system
  labels:
    app: karmada-aggregated-apiserver
    apiserver: "true"
spec:
  selector:
    matchLabels:
      app: karmada-aggregated-apiserver
      apiserver: "true"
  replicas: 2
  template:
    metadata:
      labels:
        app: karmada-aggregated-apiserver
        apiserver: "true"
    spec:
      automountServiceAccountToken: false
      containers:
        - name: karmada-aggregated-apiserver
          image: docker.io/karmada/karmada-aggregated-apiserver:latest
          imagePullPolicy: IfNotPresent
          volumeMounts:
            - name: karmada-certs
              mountPath: /etc/karmada/pki
              readOnly: true
            - name: kubeconfig
              subPath: kubeconfig
              mountPath: /etc/kubeconfig
          command:
            - /bin/karmada-aggregated-apiserver
            - --kubeconfig=/etc/kubeconfig
            - --authentication-kubeconfig=/etc/kubeconfig
            - --authorization-kubeconfig=/etc/kubeconfig
            - --etcd-servers=https://etcd-client.karmada-system.svc.cluster.local:2379
            - --etcd-cafile=/etc/karmada/pki/etcd-ca.crt
            - --etcd-certfile=/etc/karmada/pki/etcd-client.crt
            - --etcd-keyfile=/etc/karmada/pki/etcd-client.key
            - --tls-cert-file=/etc/karmada/pki/karmada.crt
            - --tls-private-key-file=/etc/karmada/pki/karmada.key
            - --audit-log-path=-
            - --feature-gates=APIPriorityAndFairness=false
            - --audit-log-maxage=0
            - --audit-log-maxbackup=0
          resources:
            requests:
              cpu: 100m
          readinessProbe:
            httpGet:
              path: /readyz
              port: 443
              scheme: HTTPS
            initialDelaySeconds: 1
            periodSeconds: 3
            timeoutSeconds: 15
          livenessProbe:
            httpGet:
              path: /healthz
              port: 443
              scheme: HTTPS
            initialDelaySeconds: 10
            periodSeconds: 10
            timeoutSeconds: 15
      volumes:
        - name: karmada-certs
          secret:
            secretName: karmada-cert-secret
        - name: kubeconfig
          secret:
            secretName: kubeconfig
---
apiVersion: v1
kind: Service
metadata:
  name: karmada-aggregated-apiserver
  namespace: karmada-system
  labels:
    app: karmada-aggregated-apiserver
    apiserver: "true"
spec:
  ports:
    - port: 443
      protocol: TCP
      targetPort: 443
  selector:
    app: karmada-aggregated-apiserver
