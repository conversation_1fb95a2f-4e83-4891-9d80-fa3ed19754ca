apiVersion: apps/v1
kind: Deployment
metadata:
  name: karmada-controller-manager
  namespace: karmada-system
  labels:
    app: karmada-controller-manager
spec:
  replicas: 2
  selector:
    matchLabels:
      app: karmada-controller-manager
  template:
    metadata:
      labels:
        app: karmada-controller-manager
    spec:
      automountServiceAccountToken: false
      tolerations:
      - key: node-role.kubernetes.io/master
        operator: Exists
      containers:
        - name: karmada-controller-manager
          image: docker.io/karmada/karmada-controller-manager:latest
          imagePullPolicy: IfNotPresent
          command:
            - /bin/karmada-controller-manager
            - --kubeconfig=/etc/kubeconfig
            - --bind-address=0.0.0.0
            - --cluster-status-update-frequency=10s
            - --secure-port=10357
            - --failover-eviction-timeout=30s
            - --v=4
          livenessProbe:
            httpGet:
              path: /healthz
              port: 10357
              scheme: HTTP
            failureThreshold: 3
            initialDelaySeconds: 15
            periodSeconds: 15
            timeoutSeconds: 5
          volumeMounts:
          - name: kubeconfig
            subPath: kubeconfig
            mountPath: /etc/kubeconfig
      volumes:
        - name: kubeconfig
          secret:
            secretName: kubeconfig
