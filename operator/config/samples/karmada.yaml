apiVersion: operator.karmada.io/v1alpha1
kind: Karmada
metadata:
  name: karmada-demo
  namespace: test
spec:
  components:
    etcd:
      local:
        imageRepository: registry.k8s.io/etcd
        imageTag: 3.5.9-0
        replicas: 1
        volumeData:
          # hostPath:
          #   type: DirectoryOrCreate
          #   path: /var/lib/karmada/etcd/karmada-demo
          volumeClaim:
            metadata:
              name: etcd-data
            spec:
              accessModes:
                - ReadWriteOnce
              resources:
                requests:
                  storage: 3Gi
    karmadaAPIServer:
      imageRepository: registry.k8s.io/kube-apiserver
      imageTag: v1.25.4
      replicas: 1
      serviceType: NodePort
      serviceSubnet: *********/12
    karmadaAggregatedAPIServer:
      imageRepository: docker.io/karmada/karmada-aggregated-apiserver
      imageTag: v1.6.0
      replicas: 1
    karmadaControllerManager:
      imageRepository: docker.io/karmada/karmada-controller-manager
      imageTag: v1.6.0
      replicas: 1
    karmadaScheduler:
      imageRepository: docker.io/karmada/karmada-scheduler
      imageTag: v1.6.0
      replicas: 1
    karmadaWebhook:
      imageRepository: docker.io/karmada/karmada-webhook
      imageTag: v1.6.0
      replicas: 1
    kubeControllerManager:
      imageRepository: registry.k8s.io/kube-controller-manager
      imageTag: v1.25.4
      replicas: 1
  hostCluster:
    networking:
      dnsDomain: cluster.local
