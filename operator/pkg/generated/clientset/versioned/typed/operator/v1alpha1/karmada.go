// Code generated by client-gen. DO NOT EDIT.

package v1alpha1

import (
	"context"
	"time"

	v1alpha1 "github.com/karmada-io/karmada/operator/pkg/apis/operator/v1alpha1"
	scheme "github.com/karmada-io/karmada/operator/pkg/generated/clientset/versioned/scheme"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	rest "k8s.io/client-go/rest"
)

// KarmadasGetter has a method to return a KarmadaInterface.
// A group's client should implement this interface.
type KarmadasGetter interface {
	Karmadas(namespace string) KarmadaInterface
}

// KarmadaInterface has methods to work with Karmada resources.
type KarmadaInterface interface {
	Create(ctx context.Context, karmada *v1alpha1.Karmada, opts v1.CreateOptions) (*v1alpha1.<PERSON>rma<PERSON>, error)
	Update(ctx context.Context, karmada *v1alpha1.Karma<PERSON>, opts v1.UpdateOptions) (*v1alpha1.Karmada, error)
	UpdateStatus(ctx context.Context, karmada *v1alpha1.Karmada, opts v1.UpdateOptions) (*v1alpha1.Karmada, error)
	Delete(ctx context.Context, name string, opts v1.DeleteOptions) error
	DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error
	Get(ctx context.Context, name string, opts v1.GetOptions) (*v1alpha1.Karmada, error)
	List(ctx context.Context, opts v1.ListOptions) (*v1alpha1.KarmadaList, error)
	Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error)
	Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *v1alpha1.Karmada, err error)
	KarmadaExpansion
}

// karmadas implements KarmadaInterface
type karmadas struct {
	client rest.Interface
	ns     string
}

// newKarmadas returns a Karmadas
func newKarmadas(c *OperatorV1alpha1Client, namespace string) *karmadas {
	return &karmadas{
		client: c.RESTClient(),
		ns:     namespace,
	}
}

// Get takes name of the karmada, and returns the corresponding karmada object, and an error if there is any.
func (c *karmadas) Get(ctx context.Context, name string, options v1.GetOptions) (result *v1alpha1.Karmada, err error) {
	result = &v1alpha1.Karmada{}
	err = c.client.Get().
		Namespace(c.ns).
		Resource("karmadas").
		Name(name).
		VersionedParams(&options, scheme.ParameterCodec).
		Do(ctx).
		Into(result)
	return
}

// List takes label and field selectors, and returns the list of Karmadas that match those selectors.
func (c *karmadas) List(ctx context.Context, opts v1.ListOptions) (result *v1alpha1.KarmadaList, err error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	result = &v1alpha1.KarmadaList{}
	err = c.client.Get().
		Namespace(c.ns).
		Resource("karmadas").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Do(ctx).
		Into(result)
	return
}

// Watch returns a watch.Interface that watches the requested karmadas.
func (c *karmadas) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	opts.Watch = true
	return c.client.Get().
		Namespace(c.ns).
		Resource("karmadas").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Watch(ctx)
}

// Create takes the representation of a karmada and creates it.  Returns the server's representation of the karmada, and an error, if there is any.
func (c *karmadas) Create(ctx context.Context, karmada *v1alpha1.Karmada, opts v1.CreateOptions) (result *v1alpha1.Karmada, err error) {
	result = &v1alpha1.Karmada{}
	err = c.client.Post().
		Namespace(c.ns).
		Resource("karmadas").
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(karmada).
		Do(ctx).
		Into(result)
	return
}

// Update takes the representation of a karmada and updates it. Returns the server's representation of the karmada, and an error, if there is any.
func (c *karmadas) Update(ctx context.Context, karmada *v1alpha1.Karmada, opts v1.UpdateOptions) (result *v1alpha1.Karmada, err error) {
	result = &v1alpha1.Karmada{}
	err = c.client.Put().
		Namespace(c.ns).
		Resource("karmadas").
		Name(karmada.Name).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(karmada).
		Do(ctx).
		Into(result)
	return
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *karmadas) UpdateStatus(ctx context.Context, karmada *v1alpha1.Karmada, opts v1.UpdateOptions) (result *v1alpha1.Karmada, err error) {
	result = &v1alpha1.Karmada{}
	err = c.client.Put().
		Namespace(c.ns).
		Resource("karmadas").
		Name(karmada.Name).
		SubResource("status").
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(karmada).
		Do(ctx).
		Into(result)
	return
}

// Delete takes name of the karmada and deletes it. Returns an error if one occurs.
func (c *karmadas) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	return c.client.Delete().
		Namespace(c.ns).
		Resource("karmadas").
		Name(name).
		Body(&opts).
		Do(ctx).
		Error()
}

// DeleteCollection deletes a collection of objects.
func (c *karmadas) DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error {
	var timeout time.Duration
	if listOpts.TimeoutSeconds != nil {
		timeout = time.Duration(*listOpts.TimeoutSeconds) * time.Second
	}
	return c.client.Delete().
		Namespace(c.ns).
		Resource("karmadas").
		VersionedParams(&listOpts, scheme.ParameterCodec).
		Timeout(timeout).
		Body(&opts).
		Do(ctx).
		Error()
}

// Patch applies the patch and returns the patched karmada.
func (c *karmadas) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *v1alpha1.Karmada, err error) {
	result = &v1alpha1.Karmada{}
	err = c.client.Patch(pt).
		Namespace(c.ns).
		Resource("karmadas").
		Name(name).
		SubResource(subresources...).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(data).
		Do(ctx).
		Into(result)
	return
}
