# This files contains all configuration options for analysis running.
# More details please refer to: https://golangci-lint.run/usage/configuration/

run:
  # timeout for analysis, e.g. 30s, 5m, default is 1m
  timeout: 10m

  # which dirs to skip: issues from them won't be reported;
  # can use regexp here: generated.*, regexp is applied on full path;
  # default value is empty list, but default dirs are skipped independently
  # from this option's value (see skip-dirs-use-default).
  # "/" will be replaced by current OS file path separator to properly work
  # on Windows.
  skip-dirs:
  - hack/tools/preferredimports # This code is directly lifted from the Kubernetes codebase, skip checking
  - (^|/)vendor($|/)
  - (^|/)third_party($|/)
  - pkg/util/lifted # This code is lifted from other projects(Kubernetes, Kubefed, and so on), skip checking.

  # default is true. Enables skipping of directories:
  #   vendor$, third_party$, testdata$, examples$, Godeps$, builtin$
  skip-dirs-use-default: false

  # One of 'readonly' and 'vendor'.
  #  - readonly: the go command is disallowed from the implicit automatic updating of go.mod described above.
  #              Instead, it fails when any changes to go.mod are needed. This setting is most useful to check
  #              that go.mod does not need updates, such as in a continuous integration and testing system.
  #  - vendor: the go command assumes that the vendor directory holds the correct copies of dependencies and ignores
  #            the dependency descriptions in go.mod.
  modules-download-mode: readonly
linters:
  enable:
  # linters maintained by golang.org
  - gofmt
  - goimports
  - govet
  # linters default enabled by golangci-lint .
  - errcheck
  - gosimple
  - ineffassign
  - staticcheck
  - typecheck
  - unused
  # other linters supported by golangci-lint.
  - gci
  - gocyclo
  - gosec
  - misspell
  - whitespace
  - revive

linters-settings:
  goimports:
    local-prefixes: github.com/karmada-io/karmada
  gocyclo:
    # minimal cyclomatic complexity to report
    min-complexity: 15
  gci:
    sections:
      - Standard
      - Default
      - Prefix(github.com/karmada-io/karmada)
  revive:
    rules:
      # Disable if-return as it is too strict and not always useful.
      # https://github.com/mgechev/revive/blob/master/RULES_DESCRIPTIONS.md#if-return
      - name: if-return
        disabled: true
  staticcheck:
    checks:
      - all
      - '-SA1019' # disable deprecation check. Tracked by https://github.com/karmada-io/karmada/issues/3835.

issues:
  # The list of ids of default excludes to include or disable. By default it's empty.
  include:
  # disable excluding of issues about comments from revive
  # see https://golangci-lint.run/usage/configuration/#command-line-options for more info
  - EXC0012
  - EXC0013
  - EXC0014
