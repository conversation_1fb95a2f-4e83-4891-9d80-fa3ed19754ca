package core

import (
	"context"
	"fmt"
	"math"
	"sort"

	"k8s.io/apimachinery/pkg/util/sets"
	"k8s.io/klog/v2"

	clusterv1alpha1 "github.com/karmada-io/karmada/pkg/apis/cluster/v1alpha1"
	policyv1alpha1 "github.com/karmada-io/karmada/pkg/apis/policy/v1alpha1"
	workv1alpha2 "github.com/karmada-io/karmada/pkg/apis/work/v1alpha2"
	estimatorclient "github.com/karmada-io/karmada/pkg/estimator/client"
	"github.com/karmada-io/karmada/pkg/util"
	"github.com/karmada-io/karmada/pkg/util/names"
)

type calculator func([]*clusterv1alpha1.Cluster, *workv1alpha2.ResourceBindingSpec) []workv1alpha2.TargetCluster

func getDefaultWeightPreference(clusters []*clusterv1alpha1.Cluster) *policyv1alpha1.ClusterPreferences {
	staticWeightLists := make([]policyv1alpha1.StaticClusterWeight, 0)
	for _, cluster := range clusters {
		staticWeightList := policyv1alpha1.StaticClusterWeight{
			TargetCluster: policyv1alpha1.ClusterAffinity{
				ClusterNames: []string{cluster.Name},
			},
			Weight: 1,
		}
		staticWeightLists = append(staticWeightLists, staticWeightList)
	}

	return &policyv1alpha1.ClusterPreferences{
		StaticWeightList: staticWeightLists,
	}
}

func calAvailableReplicas(clusters []*clusterv1alpha1.Cluster, spec *workv1alpha2.ResourceBindingSpec) []workv1alpha2.TargetCluster {
	availableTargetClusters := make([]workv1alpha2.TargetCluster, len(clusters))

	// Set the boundary.
	for i := range availableTargetClusters {
		availableTargetClusters[i].Name = clusters[i].Name
		availableTargetClusters[i].Replicas = math.MaxInt32
	}

	// For non-workload, like ServiceAccount, ConfigMap, Secret and etc, it's unnecessary to calculate available replicas in member clusters.
	// See issue: https://github.com/karmada-io/karmada/issues/3743.
	if spec.Replicas == 0 {
		klog.V(4).Infof("Do not calculate available replicas for non-workload(%s, kind=%s, %s).", spec.Resource.APIVersion,
			spec.Resource.Kind, names.NamespacedKey(spec.Resource.Namespace, spec.Resource.Name))
		return availableTargetClusters
	}

	// Get the minimum value of MaxAvailableReplicas in terms of all estimators.
	estimators := estimatorclient.GetReplicaEstimators()
	ctx := context.WithValue(context.TODO(), util.ContextKeyObject,
		fmt.Sprintf("kind=%s, name=%s/%s", spec.Resource.Kind, spec.Resource.Namespace, spec.Resource.Name))
	for _, estimator := range estimators {
		res, err := estimator.MaxAvailableReplicas(ctx, clusters, spec.ReplicaRequirements)
		if err != nil {
			klog.Errorf("Max cluster available replicas error: %v", err)
			continue
		}
		for i := range res {
			if res[i].Replicas == estimatorclient.UnauthenticReplica {
				continue
			}
			if availableTargetClusters[i].Name == res[i].Name && availableTargetClusters[i].Replicas > res[i].Replicas {
				availableTargetClusters[i].Replicas = res[i].Replicas
			}
		}
	}

	// In most cases, the target cluster max available replicas should not be MaxInt32 unless the workload is best-effort
	// and the scheduler-estimator has not been enabled. So we set the replicas to spec.Replicas for avoiding overflow.
	for i := range availableTargetClusters {
		if availableTargetClusters[i].Replicas == math.MaxInt32 {
			availableTargetClusters[i].Replicas = spec.Replicas
		}
	}

	klog.V(4).Infof("Target cluster: %v", availableTargetClusters)
	return availableTargetClusters
}

// attachZeroReplicasCluster  attach cluster in clusters into targetCluster
// The purpose is to avoid workload not appeared in rb's spec.clusters field
func attachZeroReplicasCluster(clusters []*clusterv1alpha1.Cluster, targetClusters []workv1alpha2.TargetCluster) []workv1alpha2.TargetCluster {
	targetClusterSet := sets.NewString()
	for i := range targetClusters {
		targetClusterSet.Insert(targetClusters[i].Name)
	}
	for i := range clusters {
		if !targetClusterSet.Has(clusters[i].Name) {
			targetClusters = append(targetClusters, workv1alpha2.TargetCluster{Name: clusters[i].Name, Replicas: 0})
		}
	}
	return targetClusters
}

// removeZeroReplicasCLuster remove the cluster with 0 replicas in assignResults
func removeZeroReplicasCluster(assignResults []workv1alpha2.TargetCluster) []workv1alpha2.TargetCluster {
	targetClusters := make([]workv1alpha2.TargetCluster, 0, len(assignResults))
	for _, cluster := range assignResults {
		if cluster.Replicas > 0 {
			targetClusters = append(targetClusters, workv1alpha2.TargetCluster{Name: cluster.Name, Replicas: cluster.Replicas})
		}
	}
	return targetClusters
}

func fixedClusterMigrateResult(current, desire []workv1alpha2.TargetCluster, changedReplicas int32) ([]workv1alpha2.TargetCluster, int32) {
	if changedReplicas <= 0 {
		return current, 0
	}

	var sortNames []string
	currentMap := make(map[string]workv1alpha2.TargetCluster)
	for _, cluster := range current {
		currentMap[cluster.Name] = cluster
		sortNames = append(sortNames, cluster.Name)
	}

	for _, cluster := range desire {
		if _, ok := currentMap[cluster.Name]; !ok {
			sortNames = append(sortNames, cluster.Name)
		}
	}

	var ret []workv1alpha2.TargetCluster
	diff, migrateCount := fixedClusterReplicasMigrateDiff(current, desire, changedReplicas)
	if migrateCount == 0 {
		oldPartitionSum := util.GetSumOfPartition(current)
		if oldPartitionSum == 0 {
			for i := range desire {
				cur := desire[i]
				cur.Partition = cur.Replicas
			}
		} else {
			for i := range desire {
				cur := desire[i]
				oldCluster, ok := currentMap[cur.Name]
				if ok {
					cur.Partition = oldCluster.Partition
				}
			}
		}

		// 保持原来ResourceBinding中spec.clusters的顺序，否则导致Partition失效
		sortIndex := make(map[string]int)
		for i, name := range sortNames {
			sortIndex[name] = i
		}
		sort.Slice(desire, func(i, j int) bool {
			return sortIndex[desire[i].Name] < sortIndex[desire[j].Name]
		})
		return desire, 0
	}

	oldPartitionSum := util.GetSumOfPartition(current)
	for _, cluster := range diff {
		diffReplicas := cluster.Replicas
		oldCluster, ok := currentMap[cluster.Name]
		if ok {
			cluster.Replicas = oldCluster.Replicas + diffReplicas
			cluster.Partition = oldCluster.Partition

			if diffReplicas < 0 {
				p := cluster.Partition
				if p == 0 {
					p = oldCluster.Replicas
				}
				cluster.Partition = p + diffReplicas
				if cluster.Partition < 0 {
					cluster.Partition = 0
				}
			} else if oldPartitionSum == 0 && diffReplicas == 0 && cluster.Partition == 0 {
				cluster.Partition = cluster.Replicas
			}
		}
		if cluster.Replicas > 0 {
			ret = append(ret, cluster)
		}
	}

	// 尽量保持原来的顺序
	sortIndex := make(map[string]int)
	for i, name := range sortNames {
		sortIndex[name] = i
	}
	sort.Slice(ret, func(i, j int) bool {
		return sortIndex[ret[i].Name] < sortIndex[ret[j].Name]
	})

	return ret, migrateCount
}

func fixedClusterReplicasMigrateDiff(current, desire []workv1alpha2.TargetCluster, changedReplicas int32) ([]workv1alpha2.TargetCluster, int32) {
	diffResult := diffClusterReplicasMigrate(current, desire)
	migrateCount := clusterReplicasMigrateCount(diffResult)
	if migrateCount <= changedReplicas {
		return diffResult, migrateCount
	}

	// 如果迁移的数量大于了变更的数量，那么就需要对diffResult进行调整
	migrateCount = changedReplicas

	// 分成两组。增加的 >= 0 和减少的 < 0，调整变更的数量
	var increase, decrease []workv1alpha2.TargetCluster
	for _, cluster := range diffResult {
		if cluster.Replicas >= 0 {
			increase = append(increase, cluster)
		} else {
			decrease = append(decrease, cluster)
		}
	}

	mc := migrateCount
	for i := range increase {
		cur := &increase[i]
		if mc == 0 {
			cur.Replicas = 0
			continue
		}
		if cur.Replicas >= mc {
			cur.Replicas = mc
			mc = 0
		} else {
			mc -= cur.Replicas
		}
	}

	mc = migrateCount
	for i := range decrease {
		cur := &decrease[i]
		if mc == 0 {
			cur.Replicas = 0
			continue
		}
		dmc := -cur.Replicas
		if dmc >= mc {
			cur.Replicas = -mc
			mc = 0
		} else {
			mc -= dmc
		}
	}

	ret := append(increase, decrease...)
	sort.Slice(ret, func(i, j int) bool {
		return ret[i].Replicas > ret[j].Replicas
	})
	return ret, migrateCount
}

func clusterReplicasMigrateCount(clusters []workv1alpha2.TargetCluster) int32 {
	ret := int32(0)
	for _, cluster := range clusters {
		if cluster.Replicas > 0 {
			ret += cluster.Replicas
		}
	}
	return ret
}

func diffClusterReplicasMigrate(current, desire []workv1alpha2.TargetCluster) []workv1alpha2.TargetCluster {
	currentMap := make(map[string]int32)
	for _, cluster := range current {
		currentMap[cluster.Name] = cluster.Replicas
	}

	desireMap := make(map[string]int32)
	for _, cluster := range desire {
		desireMap[cluster.Name] = cluster.Replicas
	}

	var diff []workv1alpha2.TargetCluster
	for _, cluster := range current {
		num, ok := desireMap[cluster.Name]
		if !ok {
			cluster.Replicas = -1 * cluster.Replicas
			diff = append(diff, cluster)
		} else {
			diffNum := num - cluster.Replicas
			diff = append(diff, workv1alpha2.TargetCluster{
				Name:     cluster.Name,
				Replicas: diffNum,
			})
		}
	}

	for _, cluster := range desire {
		if _, ok := currentMap[cluster.Name]; !ok {
			diff = append(diff, cluster)
		}
	}

	sort.Slice(diff, func(i, j int) bool {
		return diff[i].Replicas > diff[j].Replicas
	})
	return diff
}
