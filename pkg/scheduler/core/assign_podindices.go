package core

import (
	"context"
	"fmt"
	"sort"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	listerscorev1 "k8s.io/client-go/listers/core/v1"
	"k8s.io/klog/v2"

	workv1alpha2 "github.com/karmada-io/karmada/pkg/apis/work/v1alpha2"
)

func AssignPodIndices(ctx context.Context, spb *workv1alpha2.StatefulPodBinding, podLister listerscorev1.PodLister, targets []workv1alpha2.TargetCluster) ([]workv1alpha2.TargetCluster, error) {
	currentPodsMember, err := getCurrentPodDistribution(ctx, podLister, spb)
	if err != nil {
		return nil, err
	}
	return schedulePodsIndices(ctx, spb, currentPodsMember, targets)
}

// schedulePodsIndices
// 基于 targets 中的member 集群的副本数，将索引后缀分配到不同的member集群上，尽可能的符合上一步的副本数量，并且使得受影响的pod数量最小
// placement.ReplicaScheduling.StatefulFixedPodPlacement 配置优先级最高，剩余的按副本数分配
func schedulePodsIndices(ctx context.Context, spb *workv1alpha2.StatefulPodBinding, currentPodsMember []string, targets []workv1alpha2.TargetCluster) ([]workv1alpha2.TargetCluster, error) {
	totalReplicas := spb.Spec.Replicas
	if totalReplicas == 0 {
		return targets, nil
	}

	// 初始化结果，复制 targets 以避免修改原始数据
	result := make([]workv1alpha2.TargetCluster, len(targets))
	for i, target := range targets {
		result[i] = workv1alpha2.TargetCluster{
			Name:     target.Name,
			Replicas: target.Replicas,
		}
	}

	// 创建集群名称到索引的映射
	clusterIndexMap := make(map[string]int)
	for i, target := range result {
		clusterIndexMap[target.Name] = i
	}

	// 跟踪已分配的 Pod 索引
	assignedIndices := make(map[int32]bool)

	// 第一步：处理 StatefulFixedPodPlacement（最高优先级）
	if spb.Spec.Placement != nil &&
		spb.Spec.Placement.ReplicaScheduling != nil &&
		len(spb.Spec.Placement.ReplicaScheduling.StatefulFixedPodPlacement) > 0 {

		for _, fixedPlacement := range spb.Spec.Placement.ReplicaScheduling.StatefulFixedPodPlacement {
			clusterIdx, exists := clusterIndexMap[fixedPlacement.ClusterName]
			if !exists {
				klog.Warningf("Fixed placement references non-existent cluster: %s", fixedPlacement.ClusterName)
				continue
			}

			// 验证并分配固定的 Pod 索引
			validIndices := make([]int32, 0, len(fixedPlacement.PodIndexes))
			for _, podIndex := range fixedPlacement.PodIndexes {
				if podIndex >= 0 && podIndex < totalReplicas && !assignedIndices[podIndex] {
					validIndices = append(validIndices, podIndex)
					assignedIndices[podIndex] = true
				} else {
					klog.Warningf("Invalid or duplicate pod index %d for cluster %s", podIndex, fixedPlacement.ClusterName)
				}
			}

			// 更新目标集群的 Pod 索引
			if len(validIndices) > 0 {
				result[clusterIdx].PodIndices = validIndices
				klog.V(4).Infof("Assigned fixed pod indices %v to cluster %s", validIndices, fixedPlacement.ClusterName)
			}
		}
	}

	// 第二步：收集未分配的 Pod 索引
	unassignedIndices := make([]int32, 0)
	for i := int32(0); i < totalReplicas; i++ {
		if !assignedIndices[i] {
			unassignedIndices = append(unassignedIndices, i)
		}
	}

	// 第三步：按副本数分配剩余的 Pod 索引
	if len(unassignedIndices) > 0 {
		// 计算每个集群需要的额外副本数（减去已通过固定放置分配的）
		clusterNeeds := make([]struct {
			clusterIdx int
			needed     int32
			current    int32
		}, 0, len(result))

		for i, target := range result {
			currentAssigned := int32(len(target.PodIndices))
			needed := target.Replicas - currentAssigned
			if needed > 0 {
				clusterNeeds = append(clusterNeeds, struct {
					clusterIdx int
					needed     int32
					current    int32
				}{
					clusterIdx: i,
					needed:     needed,
					current:    currentAssigned,
				})
			}
		}

		// 第一轮：优先保持当前在该集群的 Pod
		for _, need := range clusterNeeds {
			clusterName := result[need.clusterIdx].Name

			// 找到当前已经在这个集群的未分配 Pod
			var toKeep []int32
			for i := len(unassignedIndices) - 1; i >= 0; i-- {
				podIndex := unassignedIndices[i]
				currentCluster := ""
				if int(podIndex) < len(currentPodsMember) {
					currentCluster = currentPodsMember[podIndex]
				}

				if currentCluster == clusterName {
					toKeep = append(toKeep, podIndex)
					// 从未分配列表中移除
					unassignedIndices = append(unassignedIndices[:i], unassignedIndices[i+1:]...)
				}
			}

			// 按索引排序，优先保留较小索引的 Pod（最小化干扰）
			sort.Slice(toKeep, func(i, j int) bool {
				return toKeep[i] < toKeep[j]
			})

			// 按需要的数量保留 Pod（不超过需要的数量）
			keepCount := int32(len(toKeep))
			if keepCount > need.needed {
				keepCount = need.needed
			}

			// 添加要保留的 Pod 索引（保留较小索引的）
			for i := int32(0); i < keepCount; i++ {
				result[need.clusterIdx].PodIndices = append(result[need.clusterIdx].PodIndices, toKeep[i])
			}

			// 如果保留的 Pod 超过需要的数量，将多余的放回未分配列表（较大索引的）
			for i := keepCount; i < int32(len(toKeep)); i++ {
				unassignedIndices = append(unassignedIndices, toKeep[i])
			}

			klog.V(4).Infof("Cluster %s: kept %d existing pods", clusterName, keepCount)
		}

		// 第二轮：为仍需要更多副本的集群分配剩余的 Pod
		// 确保未分配的 Pod 按索引顺序排序
		sort.Slice(unassignedIndices, func(i, j int) bool {
			return unassignedIndices[i] < unassignedIndices[j]
		})

		// 按 Pod 索引顺序分配给需要更多副本的集群
		for _, podIndex := range unassignedIndices {
			assigned := false
			// 找到第一个还需要更多副本的集群（按集群在 targets 中的顺序）
			for i := range result {
				currentAssigned := int32(len(result[i].PodIndices))
				if currentAssigned < result[i].Replicas {
					result[i].PodIndices = append(result[i].PodIndices, podIndex)
					assigned = true
					klog.V(4).Infof("Assigned pod %d to cluster %s", podIndex, result[i].Name)
					break
				}
			}

			// 如果所有集群都满了，但还有 Pod 需要分配，分配给有空间的集群
			if !assigned {
				for i := range result {
					currentAssigned := int32(len(result[i].PodIndices))
					if currentAssigned < result[i].Replicas {
						result[i].PodIndices = append(result[i].PodIndices, podIndex)
						assigned = true
						klog.V(4).Infof("Assigned pod %d to cluster %s (fallback)", podIndex, result[i].Name)
						break
					}
				}
			}

			if !assigned {
				klog.Warningf("Unable to assign pod %d to any cluster", podIndex)
			}
		}
	}

	// 第四步：对每个集群的 Pod 索引进行排序
	for i := range result {
		sort.Slice(result[i].PodIndices, func(a, b int) bool {
			return result[i].PodIndices[a] < result[i].PodIndices[b]
		})
	}

	// 验证分配结果
	if err := validatePodIndexAssignment(result, totalReplicas); err != nil {
		return nil, fmt.Errorf("pod index assignment validation failed: %w", err)
	}

	return result, nil
}

// validatePodIndexAssignment validates that all pod indices are assigned correctly
func validatePodIndexAssignment(targets []workv1alpha2.TargetCluster, totalReplicas int32) error {
	assignedIndices := make(map[int32]bool)
	totalAssigned := int32(0)

	for _, target := range targets {
		for _, podIndex := range target.PodIndices {
			if podIndex < 0 || podIndex >= totalReplicas {
				return fmt.Errorf("invalid pod index %d for cluster %s (must be 0-%d)",
					podIndex, target.Name, totalReplicas-1)
			}

			if assignedIndices[podIndex] {
				return fmt.Errorf("duplicate pod index %d assigned to cluster %s",
					podIndex, target.Name)
			}

			assignedIndices[podIndex] = true
			totalAssigned++
		}
	}

	if totalAssigned != totalReplicas {
		return fmt.Errorf("total assigned pods (%d) does not match expected replicas (%d)",
			totalAssigned, totalReplicas)
	}

	return nil
}

// getCurrentPodDistribution 获取当前副本数 每个索引所在的 member
func getCurrentPodDistribution(ctx context.Context, podLister listerscorev1.PodLister, spb *workv1alpha2.StatefulPodBinding) ([]string, error) {
	pods, err := listPods(ctx, podLister, spb)
	if err != nil {
		return nil, err
	}
	replicaCount := int(spb.Spec.Replicas)

	replicas := make([]string, replicaCount)
	for i := range pods {
		if ord := getOrdinal(pods[i]); 0 <= ord && ord < replicaCount {
			// if the ordinal of the pod is within the range of the current number of replicas and not in reserveOrdinals,
			// insert it at the indirection of its ordinal
			replicas[ord] = getMemberCluster(pods[i])
		}
	}
	return replicas, nil
}

// listPods gets the current pod for the stateful resource
func listPods(ctx context.Context, podLister listerscorev1.PodLister, spb *workv1alpha2.StatefulPodBinding) ([]*corev1.Pod, error) {
	selector := spb.Spec.PodSelector
	if selector == nil {
		return nil, fmt.Errorf("pod selector is nil")
	}

	ls, err := metav1.LabelSelectorAsSelector(selector)
	if err != nil {
		return nil, fmt.Errorf("failed to convert label selector: %w", err)
	}

	pods, err := podLister.Pods(spb.Namespace).List(ls)
	if err != nil {
		return nil, fmt.Errorf("failed to list pods: %w", err)
	}

	var ret []*corev1.Pod
	for i := range pods {
		p := pods[i]
		if isMemberOf(spb.Spec.Resource.Name, p) {
			ret = append(ret, p)
		}
	}
	return ret, nil
}
