package resources

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/klog/v2"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"

	policyv1alpha1 "github.com/karmada-io/karmada/pkg/apis/policy/v1alpha1"
	"github.com/karmada-io/karmada/pkg/util"
)

// MakePolicyByAnnotations 基于注解解析并返回 policyv1alpha1.PropagationPolicy
func MakePolicyByAnnotations(obj *unstructured.Unstructured) (*policyv1alpha1.PropagationPolicy, error) {
	annotations := obj.GetAnnotations()
	if annotations == nil {
		return nil, nil
	}

	// Check if required annotation exists
	clustersAnnotation := util.GetAnnotationValue(annotations, policyv1alpha1.AnnotationPodsPropagateCluster)
	if clustersAnnotation == "" {
		return nil, nil
	}

	// Parse cluster configuration
	placement, err := parseClusterPlacement(clustersAnnotation, annotations)
	if err != nil {
		return nil, fmt.Errorf("failed to parse cluster placement: %v", err)
	}

	policyName := fmt.Sprintf("%s-anno-policy", obj.GetName())
	// Create PropagationPolicy object (but don't persist it)
	policy := &policyv1alpha1.PropagationPolicy{
		ObjectMeta: metav1.ObjectMeta{
			Name:      policyName,
			Namespace: obj.GetNamespace(),
			Labels: map[string]string{
				policyv1alpha1.LabelPolicyCreatedByAnno: "true",
			},
		},
		Spec: policyv1alpha1.PropagationSpec{
			ResourceSelectors: []policyv1alpha1.ResourceSelector{
				{
					APIVersion: obj.GetAPIVersion(),
					Kind:       obj.GetKind(),
					Name:       obj.GetName(),
				},
			},
			Placement: *placement,
		},
	}

	klog.V(2).Infof("Parsed PropagationPolicy %s/%s from annotations for object %s/%s",
		policy.Namespace, policy.Name, obj.GetNamespace(), obj.GetName())

	return policy, nil
}

// parseClusterPlacement parses cluster placement configuration from annotations
func parseClusterPlacement(clustersAnnotation string, annotations map[string]string) (*policyv1alpha1.Placement, error) {
	placement := &policyv1alpha1.Placement{}

	// Parse cluster names and weights
	clusterWeights, err := parseClusterWeights(clustersAnnotation)
	if err != nil {
		return nil, fmt.Errorf("failed to parse cluster weights: %v", err)
	}

	// Set cluster affinity
	clusterNames := make([]string, 0, len(clusterWeights))
	staticWeights := make([]policyv1alpha1.StaticClusterWeight, 0, len(clusterWeights))

	for clusterName, weight := range clusterWeights {
		clusterNames = append(clusterNames, clusterName)
		if weight > 1 {
			staticWeights = append(staticWeights, policyv1alpha1.StaticClusterWeight{
				TargetCluster: policyv1alpha1.ClusterAffinity{
					ClusterNames: []string{clusterName},
				},
				Weight: weight,
			})
		}
	}

	placement.ClusterAffinity = &policyv1alpha1.ClusterAffinity{
		ClusterNames: clusterNames,
	}

	// Configure replica scheduling
	replicaScheduling := &policyv1alpha1.ReplicaSchedulingStrategy{
		ReplicaSchedulingType:     policyv1alpha1.ReplicaSchedulingTypeDivided,
		ReplicaDivisionPreference: policyv1alpha1.ReplicaDivisionPreferenceWeighted,
	}

	// Set weights if specified
	if len(staticWeights) > 0 {
		replicaScheduling.WeightPreference = &policyv1alpha1.ClusterPreferences{
			StaticWeightList: staticWeights,
		}
	}

	// Parse fixed pod placement if specified
	fixedPlacementAnnotation := util.GetAnnotationValue(annotations, policyv1alpha1.AnnotationPodsFixedPlacement)
	if fixedPlacementAnnotation != "" {
		fixedPlacements, err := parseFixedPodPlacement(fixedPlacementAnnotation)
		if err != nil {
			return nil, fmt.Errorf("failed to parse fixed pod placement: %v", err)
		}
		replicaScheduling.StatefulFixedPodPlacement = fixedPlacements
	}

	placement.ReplicaScheduling = replicaScheduling

	return placement, nil
}

// parseClusterWeights parses cluster names and weights from annotation value
// Supports formats like:
// - "member1,member2" (equal weights)
// - "member1:2,member2:1" (weighted)
func parseClusterWeights(clustersAnnotation string) (map[string]int64, error) {
	clusterWeights := make(map[string]int64)

	clusters := strings.Split(clustersAnnotation, ",")
	for _, cluster := range clusters {
		cluster = strings.TrimSpace(cluster)
		if cluster == "" {
			continue
		}

		// Check if weight is specified
		parts := strings.Split(cluster, ":")
		clusterName := strings.TrimSpace(parts[0])
		weight := int64(1) // default weight

		if len(parts) == 2 {
			weightStr := strings.TrimSpace(parts[1])
			parsedWeight, err := strconv.ParseInt(weightStr, 10, 64)
			if err != nil {
				return nil, fmt.Errorf("invalid weight %s for cluster %s: %v", weightStr, clusterName, err)
			}
			if parsedWeight <= 0 {
				return nil, fmt.Errorf("weight must be positive for cluster %s", clusterName)
			}
			weight = parsedWeight
		} else if len(parts) > 2 {
			return nil, fmt.Errorf("invalid cluster specification: %s", cluster)
		}

		clusterWeights[clusterName] = weight
	}

	if len(clusterWeights) == 0 {
		return nil, fmt.Errorf("no valid clusters found in annotation")
	}

	return clusterWeights, nil
}

// parseFixedPodPlacement parses fixed pod placement from annotation value
// Supports format like: "member1:[0,1],member2:[2,3]"
func parseFixedPodPlacement(fixedPlacementAnnotation string) ([]policyv1alpha1.StatefulFixedPodPlacement, error) {
	var fixedPlacements []policyv1alpha1.StatefulFixedPodPlacement

	clusterPlacements := strings.Split(fixedPlacementAnnotation, ",")
	for _, clusterPlacement := range clusterPlacements {
		clusterPlacement = strings.TrimSpace(clusterPlacement)
		if clusterPlacement == "" {
			continue
		}

		// Parse format: "clusterName:[index1,index2,...]"
		parts := strings.SplitN(clusterPlacement, ":", 2)
		if len(parts) != 2 {
			return nil, fmt.Errorf("invalid fixed placement format: %s", clusterPlacement)
		}

		clusterName := strings.TrimSpace(parts[0])
		indexesStr := strings.TrimSpace(parts[1])

		// Remove brackets and parse indexes
		indexesStr = strings.Trim(indexesStr, "[]")
		indexParts := strings.Split(indexesStr, ",")

		var podIndexes []int32
		for _, indexStr := range indexParts {
			indexStr = strings.TrimSpace(indexStr)
			if indexStr == "" {
				continue
			}

			index, err := strconv.ParseInt(indexStr, 10, 32)
			if err != nil {
				return nil, fmt.Errorf("invalid pod index %s for cluster %s: %v", indexStr, clusterName, err)
			}
			if index < 0 {
				return nil, fmt.Errorf("pod index must be non-negative for cluster %s", clusterName)
			}

			podIndexes = append(podIndexes, int32(index))
		}

		if len(podIndexes) > 0 {
			fixedPlacements = append(fixedPlacements, policyv1alpha1.StatefulFixedPodPlacement{
				ClusterName: clusterName,
				PodIndexes:  podIndexes,
			})
		}
	}

	return fixedPlacements, nil
}

// EnsurePolicy creates or updates the PropagationPolicy in the Kubernetes cluster.
// This method implements the "ensure" pattern - it will create the policy if it doesn't exist,
// or update it if it already exists.
func EnsurePolicy(ctx context.Context, client client.Client, policy *policyv1alpha1.PropagationPolicy) error {
	if policy == nil {
		return fmt.Errorf("policy cannot be nil")
	}

	klog.V(2).Infof("Ensuring PropagationPolicy %s/%s exists in cluster", policy.Namespace, policy.Name)

	// Use controller-runtime's CreateOrUpdate to implement the ensure pattern
	result, err := controllerutil.CreateOrUpdate(ctx, client, policy, func() error {
		// This function is called when the object exists and needs to be updated
		// We only update the spec and labels, preserving other metadata

		// Ensure the policy has the correct labels
		if policy.Labels == nil {
			policy.Labels = make(map[string]string)
		}
		policy.Labels[policyv1alpha1.LabelPolicyCreatedByAnno] = "true"

		// The spec should already be set correctly from MakePolicyByAnnotations
		// No additional updates needed here since the policy object already contains
		// the desired state

		return nil
	})

	if err != nil {
		return fmt.Errorf("failed to ensure PropagationPolicy %s/%s: %v", policy.Namespace, policy.Name, err)
	}

	switch result {
	case controllerutil.OperationResultCreated:
		klog.Infof("Created PropagationPolicy %s/%s", policy.Namespace, policy.Name)
	case controllerutil.OperationResultUpdated:
		klog.Infof("Updated PropagationPolicy %s/%s", policy.Namespace, policy.Name)
	case controllerutil.OperationResultNone:
		klog.V(2).Infof("PropagationPolicy %s/%s already up to date", policy.Namespace, policy.Name)
	}

	return nil
}
