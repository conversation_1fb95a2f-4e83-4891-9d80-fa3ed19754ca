// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	"context"

	v1alpha2 "github.com/karmada-io/karmada/pkg/apis/work/v1alpha2"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	testing "k8s.io/client-go/testing"
)

// FakeStatefulPodBindings implements StatefulPodBindingInterface
type FakeStatefulPodBindings struct {
	Fake *FakeWorkV1alpha2
	ns   string
}

var statefulpodbindingsResource = v1alpha2.SchemeGroupVersion.WithResource("statefulpodbindings")

var statefulpodbindingsKind = v1alpha2.SchemeGroupVersion.WithKind("StatefulPodBinding")

// Get takes name of the statefulPodBinding, and returns the corresponding statefulPodBinding object, and an error if there is any.
func (c *FakeStatefulPodBindings) Get(ctx context.Context, name string, options v1.GetOptions) (result *v1alpha2.StatefulPodBinding, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewGetAction(statefulpodbindingsResource, c.ns, name), &v1alpha2.StatefulPodBinding{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1alpha2.StatefulPodBinding), err
}

// List takes label and field selectors, and returns the list of StatefulPodBindings that match those selectors.
func (c *FakeStatefulPodBindings) List(ctx context.Context, opts v1.ListOptions) (result *v1alpha2.StatefulPodBindingList, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewListAction(statefulpodbindingsResource, statefulpodbindingsKind, c.ns, opts), &v1alpha2.StatefulPodBindingList{})

	if obj == nil {
		return nil, err
	}

	label, _, _ := testing.ExtractFromListOptions(opts)
	if label == nil {
		label = labels.Everything()
	}
	list := &v1alpha2.StatefulPodBindingList{ListMeta: obj.(*v1alpha2.StatefulPodBindingList).ListMeta}
	for _, item := range obj.(*v1alpha2.StatefulPodBindingList).Items {
		if label.Matches(labels.Set(item.Labels)) {
			list.Items = append(list.Items, item)
		}
	}
	return list, err
}

// Watch returns a watch.Interface that watches the requested statefulPodBindings.
func (c *FakeStatefulPodBindings) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	return c.Fake.
		InvokesWatch(testing.NewWatchAction(statefulpodbindingsResource, c.ns, opts))

}

// Create takes the representation of a statefulPodBinding and creates it.  Returns the server's representation of the statefulPodBinding, and an error, if there is any.
func (c *FakeStatefulPodBindings) Create(ctx context.Context, statefulPodBinding *v1alpha2.StatefulPodBinding, opts v1.CreateOptions) (result *v1alpha2.StatefulPodBinding, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewCreateAction(statefulpodbindingsResource, c.ns, statefulPodBinding), &v1alpha2.StatefulPodBinding{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1alpha2.StatefulPodBinding), err
}

// Update takes the representation of a statefulPodBinding and updates it. Returns the server's representation of the statefulPodBinding, and an error, if there is any.
func (c *FakeStatefulPodBindings) Update(ctx context.Context, statefulPodBinding *v1alpha2.StatefulPodBinding, opts v1.UpdateOptions) (result *v1alpha2.StatefulPodBinding, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewUpdateAction(statefulpodbindingsResource, c.ns, statefulPodBinding), &v1alpha2.StatefulPodBinding{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1alpha2.StatefulPodBinding), err
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *FakeStatefulPodBindings) UpdateStatus(ctx context.Context, statefulPodBinding *v1alpha2.StatefulPodBinding, opts v1.UpdateOptions) (*v1alpha2.StatefulPodBinding, error) {
	obj, err := c.Fake.
		Invokes(testing.NewUpdateSubresourceAction(statefulpodbindingsResource, "status", c.ns, statefulPodBinding), &v1alpha2.StatefulPodBinding{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1alpha2.StatefulPodBinding), err
}

// Delete takes name of the statefulPodBinding and deletes it. Returns an error if one occurs.
func (c *FakeStatefulPodBindings) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	_, err := c.Fake.
		Invokes(testing.NewDeleteActionWithOptions(statefulpodbindingsResource, c.ns, name, opts), &v1alpha2.StatefulPodBinding{})

	return err
}

// DeleteCollection deletes a collection of objects.
func (c *FakeStatefulPodBindings) DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error {
	action := testing.NewDeleteCollectionAction(statefulpodbindingsResource, c.ns, listOpts)

	_, err := c.Fake.Invokes(action, &v1alpha2.StatefulPodBindingList{})
	return err
}

// Patch applies the patch and returns the patched statefulPodBinding.
func (c *FakeStatefulPodBindings) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *v1alpha2.StatefulPodBinding, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewPatchSubresourceAction(statefulpodbindingsResource, c.ns, name, pt, data, subresources...), &v1alpha2.StatefulPodBinding{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1alpha2.StatefulPodBinding), err
}
