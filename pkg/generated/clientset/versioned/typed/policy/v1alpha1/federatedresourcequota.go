// Code generated by client-gen. DO NOT EDIT.

package v1alpha1

import (
	"context"
	"time"

	v1alpha1 "github.com/karmada-io/karmada/pkg/apis/policy/v1alpha1"
	scheme "github.com/karmada-io/karmada/pkg/generated/clientset/versioned/scheme"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	rest "k8s.io/client-go/rest"
)

// FederatedResourceQuotasGetter has a method to return a FederatedResourceQuotaInterface.
// A group's client should implement this interface.
type FederatedResourceQuotasGetter interface {
	FederatedResourceQuotas(namespace string) FederatedResourceQuotaInterface
}

// FederatedResourceQuotaInterface has methods to work with FederatedResourceQuota resources.
type FederatedResourceQuotaInterface interface {
	Create(ctx context.Context, federatedResourceQuota *v1alpha1.FederatedResourceQuota, opts v1.CreateOptions) (*v1alpha1.FederatedResourceQuota, error)
	Update(ctx context.Context, federatedResourceQuota *v1alpha1.FederatedResourceQuota, opts v1.UpdateOptions) (*v1alpha1.FederatedResourceQuota, error)
	UpdateStatus(ctx context.Context, federatedResourceQuota *v1alpha1.FederatedResourceQuota, opts v1.UpdateOptions) (*v1alpha1.FederatedResourceQuota, error)
	Delete(ctx context.Context, name string, opts v1.DeleteOptions) error
	DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error
	Get(ctx context.Context, name string, opts v1.GetOptions) (*v1alpha1.FederatedResourceQuota, error)
	List(ctx context.Context, opts v1.ListOptions) (*v1alpha1.FederatedResourceQuotaList, error)
	Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error)
	Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *v1alpha1.FederatedResourceQuota, err error)
	FederatedResourceQuotaExpansion
}

// federatedResourceQuotas implements FederatedResourceQuotaInterface
type federatedResourceQuotas struct {
	client rest.Interface
	ns     string
}

// newFederatedResourceQuotas returns a FederatedResourceQuotas
func newFederatedResourceQuotas(c *PolicyV1alpha1Client, namespace string) *federatedResourceQuotas {
	return &federatedResourceQuotas{
		client: c.RESTClient(),
		ns:     namespace,
	}
}

// Get takes name of the federatedResourceQuota, and returns the corresponding federatedResourceQuota object, and an error if there is any.
func (c *federatedResourceQuotas) Get(ctx context.Context, name string, options v1.GetOptions) (result *v1alpha1.FederatedResourceQuota, err error) {
	result = &v1alpha1.FederatedResourceQuota{}
	err = c.client.Get().
		Namespace(c.ns).
		Resource("federatedresourcequotas").
		Name(name).
		VersionedParams(&options, scheme.ParameterCodec).
		Do(ctx).
		Into(result)
	return
}

// List takes label and field selectors, and returns the list of FederatedResourceQuotas that match those selectors.
func (c *federatedResourceQuotas) List(ctx context.Context, opts v1.ListOptions) (result *v1alpha1.FederatedResourceQuotaList, err error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	result = &v1alpha1.FederatedResourceQuotaList{}
	err = c.client.Get().
		Namespace(c.ns).
		Resource("federatedresourcequotas").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Do(ctx).
		Into(result)
	return
}

// Watch returns a watch.Interface that watches the requested federatedResourceQuotas.
func (c *federatedResourceQuotas) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	opts.Watch = true
	return c.client.Get().
		Namespace(c.ns).
		Resource("federatedresourcequotas").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Watch(ctx)
}

// Create takes the representation of a federatedResourceQuota and creates it.  Returns the server's representation of the federatedResourceQuota, and an error, if there is any.
func (c *federatedResourceQuotas) Create(ctx context.Context, federatedResourceQuota *v1alpha1.FederatedResourceQuota, opts v1.CreateOptions) (result *v1alpha1.FederatedResourceQuota, err error) {
	result = &v1alpha1.FederatedResourceQuota{}
	err = c.client.Post().
		Namespace(c.ns).
		Resource("federatedresourcequotas").
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(federatedResourceQuota).
		Do(ctx).
		Into(result)
	return
}

// Update takes the representation of a federatedResourceQuota and updates it. Returns the server's representation of the federatedResourceQuota, and an error, if there is any.
func (c *federatedResourceQuotas) Update(ctx context.Context, federatedResourceQuota *v1alpha1.FederatedResourceQuota, opts v1.UpdateOptions) (result *v1alpha1.FederatedResourceQuota, err error) {
	result = &v1alpha1.FederatedResourceQuota{}
	err = c.client.Put().
		Namespace(c.ns).
		Resource("federatedresourcequotas").
		Name(federatedResourceQuota.Name).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(federatedResourceQuota).
		Do(ctx).
		Into(result)
	return
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *federatedResourceQuotas) UpdateStatus(ctx context.Context, federatedResourceQuota *v1alpha1.FederatedResourceQuota, opts v1.UpdateOptions) (result *v1alpha1.FederatedResourceQuota, err error) {
	result = &v1alpha1.FederatedResourceQuota{}
	err = c.client.Put().
		Namespace(c.ns).
		Resource("federatedresourcequotas").
		Name(federatedResourceQuota.Name).
		SubResource("status").
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(federatedResourceQuota).
		Do(ctx).
		Into(result)
	return
}

// Delete takes name of the federatedResourceQuota and deletes it. Returns an error if one occurs.
func (c *federatedResourceQuotas) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	return c.client.Delete().
		Namespace(c.ns).
		Resource("federatedresourcequotas").
		Name(name).
		Body(&opts).
		Do(ctx).
		Error()
}

// DeleteCollection deletes a collection of objects.
func (c *federatedResourceQuotas) DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error {
	var timeout time.Duration
	if listOpts.TimeoutSeconds != nil {
		timeout = time.Duration(*listOpts.TimeoutSeconds) * time.Second
	}
	return c.client.Delete().
		Namespace(c.ns).
		Resource("federatedresourcequotas").
		VersionedParams(&listOpts, scheme.ParameterCodec).
		Timeout(timeout).
		Body(&opts).
		Do(ctx).
		Error()
}

// Patch applies the patch and returns the patched federatedResourceQuota.
func (c *federatedResourceQuotas) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *v1alpha1.FederatedResourceQuota, err error) {
	result = &v1alpha1.FederatedResourceQuota{}
	err = c.client.Patch(pt).
		Namespace(c.ns).
		Resource("federatedresourcequotas").
		Name(name).
		SubResource(subresources...).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(data).
		Do(ctx).
		Into(result)
	return
}
