// Code generated by client-gen. DO NOT EDIT.

package v1alpha1

import (
	"context"
	"time"

	v1alpha1 "github.com/karmada-io/karmada/pkg/apis/policy/v1alpha1"
	scheme "github.com/karmada-io/karmada/pkg/generated/clientset/versioned/scheme"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	rest "k8s.io/client-go/rest"
)

// OverridePoliciesGetter has a method to return a OverridePolicyInterface.
// A group's client should implement this interface.
type OverridePoliciesGetter interface {
	OverridePolicies(namespace string) OverridePolicyInterface
}

// OverridePolicyInterface has methods to work with OverridePolicy resources.
type OverridePolicyInterface interface {
	Create(ctx context.Context, overridePolicy *v1alpha1.OverridePolicy, opts v1.CreateOptions) (*v1alpha1.OverridePolicy, error)
	Update(ctx context.Context, overridePolicy *v1alpha1.OverridePolicy, opts v1.UpdateOptions) (*v1alpha1.OverridePolicy, error)
	Delete(ctx context.Context, name string, opts v1.DeleteOptions) error
	DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error
	Get(ctx context.Context, name string, opts v1.GetOptions) (*v1alpha1.OverridePolicy, error)
	List(ctx context.Context, opts v1.ListOptions) (*v1alpha1.OverridePolicyList, error)
	Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error)
	Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *v1alpha1.OverridePolicy, err error)
	OverridePolicyExpansion
}

// overridePolicies implements OverridePolicyInterface
type overridePolicies struct {
	client rest.Interface
	ns     string
}

// newOverridePolicies returns a OverridePolicies
func newOverridePolicies(c *PolicyV1alpha1Client, namespace string) *overridePolicies {
	return &overridePolicies{
		client: c.RESTClient(),
		ns:     namespace,
	}
}

// Get takes name of the overridePolicy, and returns the corresponding overridePolicy object, and an error if there is any.
func (c *overridePolicies) Get(ctx context.Context, name string, options v1.GetOptions) (result *v1alpha1.OverridePolicy, err error) {
	result = &v1alpha1.OverridePolicy{}
	err = c.client.Get().
		Namespace(c.ns).
		Resource("overridepolicies").
		Name(name).
		VersionedParams(&options, scheme.ParameterCodec).
		Do(ctx).
		Into(result)
	return
}

// List takes label and field selectors, and returns the list of OverridePolicies that match those selectors.
func (c *overridePolicies) List(ctx context.Context, opts v1.ListOptions) (result *v1alpha1.OverridePolicyList, err error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	result = &v1alpha1.OverridePolicyList{}
	err = c.client.Get().
		Namespace(c.ns).
		Resource("overridepolicies").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Do(ctx).
		Into(result)
	return
}

// Watch returns a watch.Interface that watches the requested overridePolicies.
func (c *overridePolicies) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	opts.Watch = true
	return c.client.Get().
		Namespace(c.ns).
		Resource("overridepolicies").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Watch(ctx)
}

// Create takes the representation of a overridePolicy and creates it.  Returns the server's representation of the overridePolicy, and an error, if there is any.
func (c *overridePolicies) Create(ctx context.Context, overridePolicy *v1alpha1.OverridePolicy, opts v1.CreateOptions) (result *v1alpha1.OverridePolicy, err error) {
	result = &v1alpha1.OverridePolicy{}
	err = c.client.Post().
		Namespace(c.ns).
		Resource("overridepolicies").
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(overridePolicy).
		Do(ctx).
		Into(result)
	return
}

// Update takes the representation of a overridePolicy and updates it. Returns the server's representation of the overridePolicy, and an error, if there is any.
func (c *overridePolicies) Update(ctx context.Context, overridePolicy *v1alpha1.OverridePolicy, opts v1.UpdateOptions) (result *v1alpha1.OverridePolicy, err error) {
	result = &v1alpha1.OverridePolicy{}
	err = c.client.Put().
		Namespace(c.ns).
		Resource("overridepolicies").
		Name(overridePolicy.Name).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(overridePolicy).
		Do(ctx).
		Into(result)
	return
}

// Delete takes name of the overridePolicy and deletes it. Returns an error if one occurs.
func (c *overridePolicies) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	return c.client.Delete().
		Namespace(c.ns).
		Resource("overridepolicies").
		Name(name).
		Body(&opts).
		Do(ctx).
		Error()
}

// DeleteCollection deletes a collection of objects.
func (c *overridePolicies) DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error {
	var timeout time.Duration
	if listOpts.TimeoutSeconds != nil {
		timeout = time.Duration(*listOpts.TimeoutSeconds) * time.Second
	}
	return c.client.Delete().
		Namespace(c.ns).
		Resource("overridepolicies").
		VersionedParams(&listOpts, scheme.ParameterCodec).
		Timeout(timeout).
		Body(&opts).
		Do(ctx).
		Error()
}

// Patch applies the patch and returns the patched overridePolicy.
func (c *overridePolicies) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *v1alpha1.OverridePolicy, err error) {
	result = &v1alpha1.OverridePolicy{}
	err = c.client.Patch(pt).
		Namespace(c.ns).
		Resource("overridepolicies").
		Name(name).
		SubResource(subresources...).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(data).
		Do(ctx).
		Into(result)
	return
}
