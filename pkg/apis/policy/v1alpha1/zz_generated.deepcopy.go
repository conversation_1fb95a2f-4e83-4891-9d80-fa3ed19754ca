//go:build !ignore_autogenerated
// +build !ignore_autogenerated

// Code generated by deepcopy-gen. DO NOT EDIT.

package v1alpha1

import (
	corev1 "k8s.io/api/core/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	runtime "k8s.io/apimachinery/pkg/runtime"
)

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ApplicationFailoverBehavior) DeepCopyInto(out *ApplicationFailoverBehavior) {
	*out = *in
	in.DecisionConditions.DeepCopyInto(&out.DecisionConditions)
	if in.GracePeriodSeconds != nil {
		in, out := &in.GracePeriodSeconds, &out.GracePeriodSeconds
		*out = new(int32)
		**out = **in
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ApplicationFailoverBehavior.
func (in *ApplicationFailoverBehavior) DeepCopy() *ApplicationFailoverBehavior {
	if in == nil {
		return nil
	}
	out := new(ApplicationFailoverBehavior)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ClusterAffinity) DeepCopyInto(out *ClusterAffinity) {
	*out = *in
	if in.LabelSelector != nil {
		in, out := &in.LabelSelector, &out.LabelSelector
		*out = new(v1.LabelSelector)
		(*in).DeepCopyInto(*out)
	}
	if in.FieldSelector != nil {
		in, out := &in.FieldSelector, &out.FieldSelector
		*out = new(FieldSelector)
		(*in).DeepCopyInto(*out)
	}
	if in.ClusterNames != nil {
		in, out := &in.ClusterNames, &out.ClusterNames
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.ExcludeClusters != nil {
		in, out := &in.ExcludeClusters, &out.ExcludeClusters
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ClusterAffinity.
func (in *ClusterAffinity) DeepCopy() *ClusterAffinity {
	if in == nil {
		return nil
	}
	out := new(ClusterAffinity)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ClusterAffinityTerm) DeepCopyInto(out *ClusterAffinityTerm) {
	*out = *in
	in.ClusterAffinity.DeepCopyInto(&out.ClusterAffinity)
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ClusterAffinityTerm.
func (in *ClusterAffinityTerm) DeepCopy() *ClusterAffinityTerm {
	if in == nil {
		return nil
	}
	out := new(ClusterAffinityTerm)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ClusterOverridePolicy) DeepCopyInto(out *ClusterOverridePolicy) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ClusterOverridePolicy.
func (in *ClusterOverridePolicy) DeepCopy() *ClusterOverridePolicy {
	if in == nil {
		return nil
	}
	out := new(ClusterOverridePolicy)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *ClusterOverridePolicy) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ClusterOverridePolicyList) DeepCopyInto(out *ClusterOverridePolicyList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]ClusterOverridePolicy, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ClusterOverridePolicyList.
func (in *ClusterOverridePolicyList) DeepCopy() *ClusterOverridePolicyList {
	if in == nil {
		return nil
	}
	out := new(ClusterOverridePolicyList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *ClusterOverridePolicyList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ClusterPreferences) DeepCopyInto(out *ClusterPreferences) {
	*out = *in
	if in.StaticWeightList != nil {
		in, out := &in.StaticWeightList, &out.StaticWeightList
		*out = make([]StaticClusterWeight, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ClusterPreferences.
func (in *ClusterPreferences) DeepCopy() *ClusterPreferences {
	if in == nil {
		return nil
	}
	out := new(ClusterPreferences)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ClusterPropagationPolicy) DeepCopyInto(out *ClusterPropagationPolicy) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ClusterPropagationPolicy.
func (in *ClusterPropagationPolicy) DeepCopy() *ClusterPropagationPolicy {
	if in == nil {
		return nil
	}
	out := new(ClusterPropagationPolicy)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *ClusterPropagationPolicy) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ClusterPropagationPolicyList) DeepCopyInto(out *ClusterPropagationPolicyList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]ClusterPropagationPolicy, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ClusterPropagationPolicyList.
func (in *ClusterPropagationPolicyList) DeepCopy() *ClusterPropagationPolicyList {
	if in == nil {
		return nil
	}
	out := new(ClusterPropagationPolicyList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *ClusterPropagationPolicyList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ClusterQuotaStatus) DeepCopyInto(out *ClusterQuotaStatus) {
	*out = *in
	in.ResourceQuotaStatus.DeepCopyInto(&out.ResourceQuotaStatus)
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ClusterQuotaStatus.
func (in *ClusterQuotaStatus) DeepCopy() *ClusterQuotaStatus {
	if in == nil {
		return nil
	}
	out := new(ClusterQuotaStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *CommandArgsOverrider) DeepCopyInto(out *CommandArgsOverrider) {
	*out = *in
	if in.Value != nil {
		in, out := &in.Value, &out.Value
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new CommandArgsOverrider.
func (in *CommandArgsOverrider) DeepCopy() *CommandArgsOverrider {
	if in == nil {
		return nil
	}
	out := new(CommandArgsOverrider)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *DecisionConditions) DeepCopyInto(out *DecisionConditions) {
	*out = *in
	if in.TolerationSeconds != nil {
		in, out := &in.TolerationSeconds, &out.TolerationSeconds
		*out = new(int32)
		**out = **in
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new DecisionConditions.
func (in *DecisionConditions) DeepCopy() *DecisionConditions {
	if in == nil {
		return nil
	}
	out := new(DecisionConditions)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *FailoverBehavior) DeepCopyInto(out *FailoverBehavior) {
	*out = *in
	if in.Application != nil {
		in, out := &in.Application, &out.Application
		*out = new(ApplicationFailoverBehavior)
		(*in).DeepCopyInto(*out)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new FailoverBehavior.
func (in *FailoverBehavior) DeepCopy() *FailoverBehavior {
	if in == nil {
		return nil
	}
	out := new(FailoverBehavior)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *FederatedResourceQuota) DeepCopyInto(out *FederatedResourceQuota) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new FederatedResourceQuota.
func (in *FederatedResourceQuota) DeepCopy() *FederatedResourceQuota {
	if in == nil {
		return nil
	}
	out := new(FederatedResourceQuota)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *FederatedResourceQuota) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *FederatedResourceQuotaList) DeepCopyInto(out *FederatedResourceQuotaList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]FederatedResourceQuota, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new FederatedResourceQuotaList.
func (in *FederatedResourceQuotaList) DeepCopy() *FederatedResourceQuotaList {
	if in == nil {
		return nil
	}
	out := new(FederatedResourceQuotaList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *FederatedResourceQuotaList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *FederatedResourceQuotaSpec) DeepCopyInto(out *FederatedResourceQuotaSpec) {
	*out = *in
	if in.Overall != nil {
		in, out := &in.Overall, &out.Overall
		*out = make(corev1.ResourceList, len(*in))
		for key, val := range *in {
			(*out)[key] = val.DeepCopy()
		}
	}
	if in.StaticAssignments != nil {
		in, out := &in.StaticAssignments, &out.StaticAssignments
		*out = make([]StaticClusterAssignment, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new FederatedResourceQuotaSpec.
func (in *FederatedResourceQuotaSpec) DeepCopy() *FederatedResourceQuotaSpec {
	if in == nil {
		return nil
	}
	out := new(FederatedResourceQuotaSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *FederatedResourceQuotaStatus) DeepCopyInto(out *FederatedResourceQuotaStatus) {
	*out = *in
	if in.Overall != nil {
		in, out := &in.Overall, &out.Overall
		*out = make(corev1.ResourceList, len(*in))
		for key, val := range *in {
			(*out)[key] = val.DeepCopy()
		}
	}
	if in.OverallUsed != nil {
		in, out := &in.OverallUsed, &out.OverallUsed
		*out = make(corev1.ResourceList, len(*in))
		for key, val := range *in {
			(*out)[key] = val.DeepCopy()
		}
	}
	if in.AggregatedStatus != nil {
		in, out := &in.AggregatedStatus, &out.AggregatedStatus
		*out = make([]ClusterQuotaStatus, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new FederatedResourceQuotaStatus.
func (in *FederatedResourceQuotaStatus) DeepCopy() *FederatedResourceQuotaStatus {
	if in == nil {
		return nil
	}
	out := new(FederatedResourceQuotaStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *FieldSelector) DeepCopyInto(out *FieldSelector) {
	*out = *in
	if in.MatchExpressions != nil {
		in, out := &in.MatchExpressions, &out.MatchExpressions
		*out = make([]corev1.NodeSelectorRequirement, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new FieldSelector.
func (in *FieldSelector) DeepCopy() *FieldSelector {
	if in == nil {
		return nil
	}
	out := new(FieldSelector)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ImageOverrider) DeepCopyInto(out *ImageOverrider) {
	*out = *in
	if in.Predicate != nil {
		in, out := &in.Predicate, &out.Predicate
		*out = new(ImagePredicate)
		**out = **in
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ImageOverrider.
func (in *ImageOverrider) DeepCopy() *ImageOverrider {
	if in == nil {
		return nil
	}
	out := new(ImageOverrider)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ImagePredicate) DeepCopyInto(out *ImagePredicate) {
	*out = *in
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ImagePredicate.
func (in *ImagePredicate) DeepCopy() *ImagePredicate {
	if in == nil {
		return nil
	}
	out := new(ImagePredicate)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *LabelAnnotationOverrider) DeepCopyInto(out *LabelAnnotationOverrider) {
	*out = *in
	if in.Value != nil {
		in, out := &in.Value, &out.Value
		*out = make(map[string]string, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new LabelAnnotationOverrider.
func (in *LabelAnnotationOverrider) DeepCopy() *LabelAnnotationOverrider {
	if in == nil {
		return nil
	}
	out := new(LabelAnnotationOverrider)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *OverridePolicy) DeepCopyInto(out *OverridePolicy) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new OverridePolicy.
func (in *OverridePolicy) DeepCopy() *OverridePolicy {
	if in == nil {
		return nil
	}
	out := new(OverridePolicy)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *OverridePolicy) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *OverridePolicyList) DeepCopyInto(out *OverridePolicyList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]OverridePolicy, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new OverridePolicyList.
func (in *OverridePolicyList) DeepCopy() *OverridePolicyList {
	if in == nil {
		return nil
	}
	out := new(OverridePolicyList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *OverridePolicyList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *OverrideSpec) DeepCopyInto(out *OverrideSpec) {
	*out = *in
	if in.ResourceSelectors != nil {
		in, out := &in.ResourceSelectors, &out.ResourceSelectors
		*out = make([]ResourceSelector, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.OverrideRules != nil {
		in, out := &in.OverrideRules, &out.OverrideRules
		*out = make([]RuleWithCluster, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.TargetCluster != nil {
		in, out := &in.TargetCluster, &out.TargetCluster
		*out = new(ClusterAffinity)
		(*in).DeepCopyInto(*out)
	}
	in.Overriders.DeepCopyInto(&out.Overriders)
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new OverrideSpec.
func (in *OverrideSpec) DeepCopy() *OverrideSpec {
	if in == nil {
		return nil
	}
	out := new(OverrideSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Overriders) DeepCopyInto(out *Overriders) {
	*out = *in
	if in.Plaintext != nil {
		in, out := &in.Plaintext, &out.Plaintext
		*out = make([]PlaintextOverrider, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.ImageOverrider != nil {
		in, out := &in.ImageOverrider, &out.ImageOverrider
		*out = make([]ImageOverrider, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.CommandOverrider != nil {
		in, out := &in.CommandOverrider, &out.CommandOverrider
		*out = make([]CommandArgsOverrider, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.ArgsOverrider != nil {
		in, out := &in.ArgsOverrider, &out.ArgsOverrider
		*out = make([]CommandArgsOverrider, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.LabelsOverrider != nil {
		in, out := &in.LabelsOverrider, &out.LabelsOverrider
		*out = make([]LabelAnnotationOverrider, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.AnnotationsOverrider != nil {
		in, out := &in.AnnotationsOverrider, &out.AnnotationsOverrider
		*out = make([]LabelAnnotationOverrider, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Overriders.
func (in *Overriders) DeepCopy() *Overriders {
	if in == nil {
		return nil
	}
	out := new(Overriders)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Placement) DeepCopyInto(out *Placement) {
	*out = *in
	if in.ClusterAffinity != nil {
		in, out := &in.ClusterAffinity, &out.ClusterAffinity
		*out = new(ClusterAffinity)
		(*in).DeepCopyInto(*out)
	}
	if in.ClusterAffinities != nil {
		in, out := &in.ClusterAffinities, &out.ClusterAffinities
		*out = make([]ClusterAffinityTerm, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.ClusterTolerations != nil {
		in, out := &in.ClusterTolerations, &out.ClusterTolerations
		*out = make([]corev1.Toleration, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	if in.SpreadConstraints != nil {
		in, out := &in.SpreadConstraints, &out.SpreadConstraints
		*out = make([]SpreadConstraint, len(*in))
		copy(*out, *in)
	}
	if in.ReplicaScheduling != nil {
		in, out := &in.ReplicaScheduling, &out.ReplicaScheduling
		*out = new(ReplicaSchedulingStrategy)
		(*in).DeepCopyInto(*out)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Placement.
func (in *Placement) DeepCopy() *Placement {
	if in == nil {
		return nil
	}
	out := new(Placement)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *PlaintextOverrider) DeepCopyInto(out *PlaintextOverrider) {
	*out = *in
	in.Value.DeepCopyInto(&out.Value)
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new PlaintextOverrider.
func (in *PlaintextOverrider) DeepCopy() *PlaintextOverrider {
	if in == nil {
		return nil
	}
	out := new(PlaintextOverrider)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *PropagationPolicy) DeepCopyInto(out *PropagationPolicy) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new PropagationPolicy.
func (in *PropagationPolicy) DeepCopy() *PropagationPolicy {
	if in == nil {
		return nil
	}
	out := new(PropagationPolicy)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *PropagationPolicy) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *PropagationPolicyList) DeepCopyInto(out *PropagationPolicyList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]PropagationPolicy, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new PropagationPolicyList.
func (in *PropagationPolicyList) DeepCopy() *PropagationPolicyList {
	if in == nil {
		return nil
	}
	out := new(PropagationPolicyList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *PropagationPolicyList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *PropagationSpec) DeepCopyInto(out *PropagationSpec) {
	*out = *in
	if in.ResourceSelectors != nil {
		in, out := &in.ResourceSelectors, &out.ResourceSelectors
		*out = make([]ResourceSelector, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	in.Placement.DeepCopyInto(&out.Placement)
	if in.Priority != nil {
		in, out := &in.Priority, &out.Priority
		*out = new(int32)
		**out = **in
	}
	if in.DependentOverrides != nil {
		in, out := &in.DependentOverrides, &out.DependentOverrides
		*out = make([]string, len(*in))
		copy(*out, *in)
	}
	if in.Failover != nil {
		in, out := &in.Failover, &out.Failover
		*out = new(FailoverBehavior)
		(*in).DeepCopyInto(*out)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new PropagationSpec.
func (in *PropagationSpec) DeepCopy() *PropagationSpec {
	if in == nil {
		return nil
	}
	out := new(PropagationSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ReplicaSchedulingStrategy) DeepCopyInto(out *ReplicaSchedulingStrategy) {
	*out = *in
	if in.WeightPreference != nil {
		in, out := &in.WeightPreference, &out.WeightPreference
		*out = new(ClusterPreferences)
		(*in).DeepCopyInto(*out)
	}
	if in.StatefulFixedPodPlacement != nil {
		in, out := &in.StatefulFixedPodPlacement, &out.StatefulFixedPodPlacement
		*out = make([]StatefulFixedPodPlacement, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ReplicaSchedulingStrategy.
func (in *ReplicaSchedulingStrategy) DeepCopy() *ReplicaSchedulingStrategy {
	if in == nil {
		return nil
	}
	out := new(ReplicaSchedulingStrategy)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ResourceSelector) DeepCopyInto(out *ResourceSelector) {
	*out = *in
	if in.LabelSelector != nil {
		in, out := &in.LabelSelector, &out.LabelSelector
		*out = new(v1.LabelSelector)
		(*in).DeepCopyInto(*out)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ResourceSelector.
func (in *ResourceSelector) DeepCopy() *ResourceSelector {
	if in == nil {
		return nil
	}
	out := new(ResourceSelector)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RuleWithCluster) DeepCopyInto(out *RuleWithCluster) {
	*out = *in
	if in.TargetCluster != nil {
		in, out := &in.TargetCluster, &out.TargetCluster
		*out = new(ClusterAffinity)
		(*in).DeepCopyInto(*out)
	}
	in.Overriders.DeepCopyInto(&out.Overriders)
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RuleWithCluster.
func (in *RuleWithCluster) DeepCopy() *RuleWithCluster {
	if in == nil {
		return nil
	}
	out := new(RuleWithCluster)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *SpreadConstraint) DeepCopyInto(out *SpreadConstraint) {
	*out = *in
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new SpreadConstraint.
func (in *SpreadConstraint) DeepCopy() *SpreadConstraint {
	if in == nil {
		return nil
	}
	out := new(SpreadConstraint)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *StatefulFixedPodPlacement) DeepCopyInto(out *StatefulFixedPodPlacement) {
	*out = *in
	if in.PodIndexes != nil {
		in, out := &in.PodIndexes, &out.PodIndexes
		*out = make([]int32, len(*in))
		copy(*out, *in)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new StatefulFixedPodPlacement.
func (in *StatefulFixedPodPlacement) DeepCopy() *StatefulFixedPodPlacement {
	if in == nil {
		return nil
	}
	out := new(StatefulFixedPodPlacement)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *StaticClusterAssignment) DeepCopyInto(out *StaticClusterAssignment) {
	*out = *in
	if in.Hard != nil {
		in, out := &in.Hard, &out.Hard
		*out = make(corev1.ResourceList, len(*in))
		for key, val := range *in {
			(*out)[key] = val.DeepCopy()
		}
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new StaticClusterAssignment.
func (in *StaticClusterAssignment) DeepCopy() *StaticClusterAssignment {
	if in == nil {
		return nil
	}
	out := new(StaticClusterAssignment)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *StaticClusterWeight) DeepCopyInto(out *StaticClusterWeight) {
	*out = *in
	in.TargetCluster.DeepCopyInto(&out.TargetCluster)
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new StaticClusterWeight.
func (in *StaticClusterWeight) DeepCopy() *StaticClusterWeight {
	if in == nil {
		return nil
	}
	out := new(StaticClusterWeight)
	in.DeepCopyInto(out)
	return out
}
