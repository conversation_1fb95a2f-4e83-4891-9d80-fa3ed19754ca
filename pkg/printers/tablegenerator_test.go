/*
Copyright 2019 The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package printers

import (
	"fmt"
	"reflect"
	"testing"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	metav1beta1 "k8s.io/apimachinery/pkg/apis/meta/v1beta1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/schema"
)

type TestPrintType struct {
	Data string
}

func (obj *TestPrintType) GetObjectKind() schema.ObjectKind { return schema.EmptyObjectKind }
func (obj *TestPrintType) DeepCopyObject() runtime.Object {
	if obj == nil {
		return nil
	}
	clone := *obj
	return &clone
}

func PrintCustomType(obj *TestPrintType, _ GenerateOptions) ([]metav1beta1.TableRow, error) {
	return []metav1beta1.TableRow{{Cells: []interface{}{obj.Data}}}, nil
}

func ErrorPrintHandler(_ *TestPrintType, _ GenerateOptions) ([]metav1beta1.TableRow, error) {
	return nil, fmt.Errorf("ErrorPrintHandler error")
}

func PrintCoreV1Pod(obj *corev1.Pod, _ GenerateOptions) ([]metav1beta1.TableRow, error) {
	return []metav1beta1.TableRow{{Cells: []interface{}{obj.Namespace, obj.Name}}}, nil
}

func TestCustomTypePrinting(t *testing.T) {
	columns := []metav1beta1.TableColumnDefinition{{Name: "Data"}}
	generator := NewTableGenerator()
	err := generator.TableHandler(columns, PrintCustomType)
	if err != nil {
		t.Fatalf("An error occurred when adds a print handler with a given set of columns: %#v", err)
	}

	obj := TestPrintType{"test object"}
	table, err := generator.GenerateTable(&obj, GenerateOptions{})
	if err != nil {
		t.Fatalf("An error occurred generating the table for custom type: %#v", err)
	}

	expectedTable := &metav1.Table{
		ColumnDefinitions: []metav1.TableColumnDefinition{{Name: "Data"}},
		Rows:              []metav1.TableRow{{Cells: []interface{}{"test object"}}},
	}
	if !reflect.DeepEqual(expectedTable, table) {
		t.Errorf("Error generating table from custom type. Expected (%#v), got (%#v)", expectedTable, table)
	}
}

func TestPrintHandlerError(t *testing.T) {
	columns := []metav1beta1.TableColumnDefinition{{Name: "Data"}}
	generator := NewTableGenerator()
	err := generator.TableHandler(columns, ErrorPrintHandler)
	if err != nil {
		t.Fatalf("An error occurred when adds a print handler with a given set of columns: %#v", err)
	}

	obj := TestPrintType{"test object"}
	_, err = generator.GenerateTable(&obj, GenerateOptions{})
	if err == nil || err.Error() != "ErrorPrintHandler error" {
		t.Errorf("Did not get the expected error: %#v", err)
	}
}

func TestPrintCoreV1Pod(t *testing.T) {
	columns := []metav1beta1.TableColumnDefinition{
		{Name: "Namespace"},
		{Name: "Name"},
	}
	generator := NewTableGenerator()
	err := generator.TableHandler(columns, PrintCoreV1Pod)
	if err != nil {
		t.Fatalf("An error occurred when adds a print handler with a given set of columns: %#v", err)
	}

	// Test with typed Pod object
	pod := &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Namespace: "test-namespace",
			Name:      "test-pod",
		},
	}

	table, err := generator.GenerateTable(pod, GenerateOptions{})
	if err != nil {
		t.Fatalf("An error occurred generating the table for Pod: %#v", err)
	}

	expectedTable := &metav1.Table{
		ColumnDefinitions: []metav1.TableColumnDefinition{
			{Name: "Namespace"},
			{Name: "Name"},
		},
		Rows: []metav1.TableRow{{Cells: []interface{}{"test-namespace", "test-pod"}}},
	}
	if !reflect.DeepEqual(expectedTable, table) {
		t.Errorf("Error generating table from Pod. Expected (%#v), got (%#v)", expectedTable, table)
	}

	// Test with unstructured Pod object (testing the new type conversion logic)
	unstructuredPod := &unstructured.Unstructured{
		Object: map[string]interface{}{
			"apiVersion": "v1",
			"kind":       "Pod",
			"metadata": map[string]interface{}{
				"namespace": "unstructured-namespace",
				"name":      "unstructured-pod",
			},
		},
	}
	// Set the GVK explicitly for the unstructured object
	unstructuredPod.SetGroupVersionKind(schema.GroupVersionKind{
		Group:   "",
		Version: "v1",
		Kind:    "Pod",
	})

	table2, err := generator.GenerateTable(unstructuredPod, GenerateOptions{})
	if err != nil {
		t.Fatalf("An error occurred generating the table for unstructured Pod: %#v", err)
	}

	expectedTable2 := &metav1.Table{
		ColumnDefinitions: []metav1.TableColumnDefinition{
			{Name: "Namespace"},
			{Name: "Name"},
		},
		Rows: []metav1.TableRow{{Cells: []interface{}{"unstructured-namespace", "unstructured-pod"}}},
	}
	if !reflect.DeepEqual(expectedTable2, table2) {
		t.Errorf("Error generating table from unstructured Pod. Expected (%#v), got (%#v)", expectedTable2, table2)
	}
}

func printPodList(podList *corev1.PodList, options GenerateOptions) ([]metav1.TableRow, error) {
	rows := make([]metav1.TableRow, 0, len(podList.Items))
	for i := range podList.Items {
		r, err := PrintCoreV1Pod(&podList.Items[i], options)
		if err != nil {
			return nil, err
		}
		rows = append(rows, r...)
	}
	return rows, nil
}

func TestPrintCoreV1PodList(t *testing.T) {
	generator := NewTableGenerator()
	// Register handler for individual Pod
	generator.TableHandler([]metav1.TableColumnDefinition{
		{Name: "Namespace"},
		{Name: "Name"},
	}, PrintCoreV1Pod)
	// Register handler for PodList
	generator.TableHandler([]metav1.TableColumnDefinition{
		{Name: "Namespace"},
		{Name: "Name"},
	}, printPodList)

	// Test with structured PodList
	podList := &corev1.PodList{
		Items: []corev1.Pod{
			{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: "test-namespace-1",
					Name:      "test-pod-1",
				},
			},
			{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: "test-namespace-2",
					Name:      "test-pod-2",
				},
			},
		},
	}

	table, err := generator.GenerateTable(podList, GenerateOptions{})
	if err != nil {
		t.Fatalf("An error occurred generating the table for PodList: %#v", err)
	}

	expectedTable := &metav1.Table{
		ColumnDefinitions: []metav1.TableColumnDefinition{
			{Name: "Namespace"},
			{Name: "Name"},
		},
		Rows: []metav1.TableRow{
			{Cells: []interface{}{"test-namespace-1", "test-pod-1"}},
			{Cells: []interface{}{"test-namespace-2", "test-pod-2"}},
		},
	}
	if !reflect.DeepEqual(expectedTable, table) {
		t.Errorf("Error generating table from PodList. Expected (%#v), got (%#v)", expectedTable, table)
	}

	// Test with unstructured PodList
	unstructuredPodList := &unstructured.UnstructuredList{
		Items: []unstructured.Unstructured{
			{
				Object: map[string]interface{}{
					"metadata": map[string]interface{}{
						"namespace": "unstructured-namespace-1",
						"name":      "unstructured-pod-1",
					},
				},
			},
			{
				Object: map[string]interface{}{
					"metadata": map[string]interface{}{
						"namespace": "unstructured-namespace-2",
						"name":      "unstructured-pod-2",
					},
				},
			},
		},
	}

	// Set the GVK explicitly for the unstructured list
	unstructuredPodList.SetGroupVersionKind(schema.GroupVersionKind{
		Group:   "",
		Version: "v1",
		Kind:    "PodList",
	})

	// Set GVK for each item in the list
	for i := range unstructuredPodList.Items {
		unstructuredPodList.Items[i].SetGroupVersionKind(schema.GroupVersionKind{
			Group:   "",
			Version: "v1",
			Kind:    "Pod",
		})
	}

	table2, err := generator.GenerateTable(unstructuredPodList, GenerateOptions{})
	if err != nil {
		t.Fatalf("An error occurred generating the table for unstructured PodList: %#v", err)
	}

	expectedTable2 := &metav1.Table{
		ColumnDefinitions: []metav1.TableColumnDefinition{
			{Name: "Namespace"},
			{Name: "Name"},
		},
		Rows: []metav1.TableRow{
			{Cells: []interface{}{"unstructured-namespace-1", "unstructured-pod-1"}},
			{Cells: []interface{}{"unstructured-namespace-2", "unstructured-pod-2"}},
		},
	}
	if !reflect.DeepEqual(expectedTable2, table2) {
		t.Errorf("Error generating table from unstructured PodList. Expected (%#v), got (%#v)", expectedTable2, table2)
	}
}
