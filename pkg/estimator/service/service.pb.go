// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: service.proto

package service

import (
	context "context"
	fmt "fmt"
	proto "github.com/gogo/protobuf/proto"
	pb "github.com/karmada-io/karmada/pkg/estimator/pb"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	math "math"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

func init() { proto.RegisterFile("service.proto", fileDescriptor_a0b84a42fa06f626) }

var fileDescriptor_a0b84a42fa06f626 = []byte{
	// 205 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0xe2, 0x2d, 0x4e, 0x2d, 0x2a,
	0xcb, 0x4c, 0x4e, 0xd5, 0x2b, 0x28, 0xca, 0x2f, 0xc9, 0x17, 0x32, 0x4e, 0xcf, 0x2c, 0xc9, 0x28,
	0x4d, 0xd2, 0x4b, 0xce, 0xcf, 0xd5, 0xcb, 0x4e, 0x2c, 0xca, 0x4d, 0x4c, 0x49, 0x8c, 0xcf, 0xcc,
	0x87, 0x31, 0xf5, 0x0a, 0xb2, 0xd3, 0xf5, 0x52, 0x8b, 0x4b, 0x32, 0x73, 0x13, 0x4b, 0xf2, 0x8b,
	0xf4, 0xa0, 0x5a, 0xa5, 0x14, 0x0a, 0xb2, 0xd3, 0xf5, 0xe1, 0xc2, 0xfa, 0x05, 0x49, 0xfa, 0xe9,
	0xa9, 0x79, 0xa9, 0x45, 0x89, 0x25, 0xa9, 0x29, 0x10, 0x63, 0x8d, 0xfe, 0x32, 0x71, 0x71, 0xba,
	0xc2, 0x14, 0x08, 0x6d, 0x66, 0xe4, 0x12, 0xf1, 0x4d, 0xac, 0x70, 0x2c, 0x4b, 0xcc, 0xcc, 0x49,
	0x4c, 0xca, 0x49, 0x0d, 0x4a, 0x2d, 0xc8, 0xc9, 0x4c, 0x4e, 0x2c, 0x16, 0xf2, 0xd6, 0x23, 0xc5,
	0xfa, 0x82, 0x24, 0x3d, 0x6c, 0xa6, 0x04, 0xa5, 0x16, 0x96, 0xa6, 0x16, 0x97, 0x48, 0xf9, 0x50,
	0xc7, 0xb0, 0xe2, 0x82, 0xfc, 0xbc, 0xe2, 0x54, 0x25, 0x06, 0xa1, 0x9d, 0x8c, 0x5c, 0x12, 0xee,
	0xa9, 0x25, 0xa1, 0x79, 0xc5, 0xc9, 0x19, 0xa9, 0x29, 0xa5, 0xa8, 0x2e, 0x27, 0xd9, 0x32, 0xac,
	0xc6, 0xc0, 0x9c, 0xee, 0x4b, 0x25, 0xd3, 0x60, 0x6e, 0x77, 0xe2, 0x8c, 0x62, 0x87, 0x46, 0x16,
	0x20, 0x00, 0x00, 0xff, 0xff, 0x9f, 0xe1, 0x7a, 0x70, 0xf1, 0x01, 0x00, 0x00,
}

// Reference imports to suppress errors if they are not otherwise used.
var _ context.Context
var _ grpc.ClientConn

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
const _ = grpc.SupportPackageIsVersion4

// EstimatorClient is the client API for Estimator service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://godoc.org/google.golang.org/grpc#ClientConn.NewStream.
type EstimatorClient interface {
	MaxAvailableReplicas(ctx context.Context, in *pb.MaxAvailableReplicasRequest, opts ...grpc.CallOption) (*pb.MaxAvailableReplicasResponse, error)
	GetUnschedulableReplicas(ctx context.Context, in *pb.UnschedulableReplicasRequest, opts ...grpc.CallOption) (*pb.UnschedulableReplicasResponse, error)
}

type estimatorClient struct {
	cc *grpc.ClientConn
}

func NewEstimatorClient(cc *grpc.ClientConn) EstimatorClient {
	return &estimatorClient{cc}
}

func (c *estimatorClient) MaxAvailableReplicas(ctx context.Context, in *pb.MaxAvailableReplicasRequest, opts ...grpc.CallOption) (*pb.MaxAvailableReplicasResponse, error) {
	out := new(pb.MaxAvailableReplicasResponse)
	err := c.cc.Invoke(ctx, "/github.com.karmada_io.karmada.pkg.estimator.service.Estimator/MaxAvailableReplicas", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *estimatorClient) GetUnschedulableReplicas(ctx context.Context, in *pb.UnschedulableReplicasRequest, opts ...grpc.CallOption) (*pb.UnschedulableReplicasResponse, error) {
	out := new(pb.UnschedulableReplicasResponse)
	err := c.cc.Invoke(ctx, "/github.com.karmada_io.karmada.pkg.estimator.service.Estimator/GetUnschedulableReplicas", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// EstimatorServer is the server API for Estimator service.
type EstimatorServer interface {
	MaxAvailableReplicas(context.Context, *pb.MaxAvailableReplicasRequest) (*pb.MaxAvailableReplicasResponse, error)
	GetUnschedulableReplicas(context.Context, *pb.UnschedulableReplicasRequest) (*pb.UnschedulableReplicasResponse, error)
}

// UnimplementedEstimatorServer can be embedded to have forward compatible implementations.
type UnimplementedEstimatorServer struct {
}

func (*UnimplementedEstimatorServer) MaxAvailableReplicas(ctx context.Context, req *pb.MaxAvailableReplicasRequest) (*pb.MaxAvailableReplicasResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MaxAvailableReplicas not implemented")
}
func (*UnimplementedEstimatorServer) GetUnschedulableReplicas(ctx context.Context, req *pb.UnschedulableReplicasRequest) (*pb.UnschedulableReplicasResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUnschedulableReplicas not implemented")
}

func RegisterEstimatorServer(s *grpc.Server, srv EstimatorServer) {
	s.RegisterService(&_Estimator_serviceDesc, srv)
}

func _Estimator_MaxAvailableReplicas_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pb.MaxAvailableReplicasRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EstimatorServer).MaxAvailableReplicas(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/github.com.karmada_io.karmada.pkg.estimator.service.Estimator/MaxAvailableReplicas",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EstimatorServer).MaxAvailableReplicas(ctx, req.(*pb.MaxAvailableReplicasRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Estimator_GetUnschedulableReplicas_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(pb.UnschedulableReplicasRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EstimatorServer).GetUnschedulableReplicas(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/github.com.karmada_io.karmada.pkg.estimator.service.Estimator/GetUnschedulableReplicas",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EstimatorServer).GetUnschedulableReplicas(ctx, req.(*pb.UnschedulableReplicasRequest))
	}
	return interceptor(ctx, in, info, handler)
}

var _Estimator_serviceDesc = grpc.ServiceDesc{
	ServiceName: "github.com.karmada_io.karmada.pkg.estimator.service.Estimator",
	HandlerType: (*EstimatorServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "MaxAvailableReplicas",
			Handler:    _Estimator_MaxAvailableReplicas_Handler,
		},
		{
			MethodName: "GetUnschedulableReplicas",
			Handler:    _Estimator_GetUnschedulableReplicas_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service.proto",
}
