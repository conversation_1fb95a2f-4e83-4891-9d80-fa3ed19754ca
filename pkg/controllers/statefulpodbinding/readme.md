# StatefulPodBinding Controller

### 功能一：调协 StatefulPodBinding，调度副本数到不同的member
1. 基于分发策略，workload 的副本数拆分到不同的member集群上去
2. 基于当前pod 的分布，将有状态pod的索引后缀进行分配到不同的member上去，尽可能的符合上一步的副本数量，并且使得受影响的pod数量最小

策略解释如下：
对于按 Pod 粒度分发，replicaScheduling 策略允许灵活控制每个 Pod 的去向：
1. Placement *policyv1alpha1.Placement 提供了一种基于权重的通用分配方法。当 StatefulSet 伸缩（增加副本）或分发策略中集群权重发生变化时，此策略主要用于决定新增的、或尚未分配成员集群的 Pod应该被分配到哪个集群。它旨在根据权重比例动态平衡新 Pod 在各集群间的分布。
2. 其中 fixedPodPlacement 提供了更细致的控制能力，允许用户将特定的、具有关键序号的 Pod（例如主节点、特定数据分片）精确地部署到指定的成员集群中。此配置具有最高优先级。一旦 Pod 通过 fixedPodPlacement 指定了成员集群，除非此配置自身发生变更，否则控制器不会主动将其迁移到其他集群，即使 weightedPlacement 的权重发生变化

如果 fixedPodPlacement 中指定的member不在权重配置中，则将该member 添加到结果中。

最终将结果写到 StatefulPodBinding Spec 的 Clusters []PodTargetCluster 中去。

### 功能二：
通过 类似于调度器的方式 创建binding 对象的方式给没有分配node 的pod写上nodeName. 格式为 vk-{member-cluster-name}



