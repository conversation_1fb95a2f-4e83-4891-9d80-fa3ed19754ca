package statefulpodbinding

import (
	"context"
	"fmt"

	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/klog/v2"
	"sigs.k8s.io/controller-runtime/pkg/client"

	workv1alpha2 "github.com/karmada-io/karmada/pkg/apis/work/v1alpha2"
	"github.com/karmada-io/karmada/pkg/resourceinterpreter"
	"github.com/karmada-io/karmada/pkg/util/helper"
)

// StatefulWorkload represents an abstraction for stateful workloads that support pod-level scheduling
type StatefulWorkload interface {
	// GetReplicas returns the desired number of replicas
	GetReplicas() int32
	
	// GetGVK returns the GroupVersionKind of the workload
	GetGVK() schema.GroupVersionKind
	
	// GetNamespacedName returns the namespaced name of the workload
	GetNamespacedName() types.NamespacedName
	
	// GetObject returns the underlying unstructured object
	GetObject() *unstructured.Unstructured
}

// WorkloadFactory creates StatefulWorkload instances from different workload types
type WorkloadFactory struct {
	client              client.Client
	resourceInterpreter resourceinterpreter.ResourceInterpreter
}

// NewWorkloadFactory creates a new WorkloadFactory
func NewWorkloadFactory(client client.Client, resourceInterpreter resourceinterpreter.ResourceInterpreter) *WorkloadFactory {
	return &WorkloadFactory{
		client:              client,
		resourceInterpreter: resourceInterpreter,
	}
}

// GetWorkload retrieves and wraps a workload referenced by StatefulPodBinding
func (f *WorkloadFactory) GetWorkload(ctx context.Context, spb *workv1alpha2.StatefulPodBinding) (StatefulWorkload, error) {
	// Parse the GVK from the resource reference
	gvk := schema.GroupVersionKind{
		Group:   spb.Spec.Resource.APIVersion,
		Version: spb.Spec.Resource.APIVersion,
		Kind:    spb.Spec.Resource.Kind,
	}
	
	// Handle APIVersion that includes group (e.g., "apps/v1")
	if gv, err := schema.ParseGroupVersion(spb.Spec.Resource.APIVersion); err == nil {
		gvk.Group = gv.Group
		gvk.Version = gv.Version
	}

	// Get the workload object
	obj := &unstructured.Unstructured{}
	obj.SetGroupVersionKind(gvk)
	
	namespacedName := types.NamespacedName{
		Namespace: spb.Spec.Resource.Namespace,
		Name:      spb.Spec.Resource.Name,
	}
	
	if err := f.client.Get(ctx, namespacedName, obj); err != nil {
		return nil, fmt.Errorf("failed to get workload %s: %w", namespacedName, err)
	}

	// Create the appropriate workload wrapper
	return f.createWorkload(obj, namespacedName)
}

// createWorkload creates a StatefulWorkload wrapper based on the workload type
func (f *WorkloadFactory) createWorkload(obj *unstructured.Unstructured, namespacedName types.NamespacedName) (StatefulWorkload, error) {
	gvk := obj.GroupVersionKind()
	
	// Try to get replicas using resource interpreter
	replicas, _, err := f.resourceInterpreter.GetReplicas(obj)
	if err != nil {
		klog.Warningf("Failed to get replicas using resource interpreter for %s: %v, trying fallback methods", gvk, err)
		// Fallback to manual extraction
		replicas, err = f.extractReplicasFromObject(obj)
		if err != nil {
			return nil, fmt.Errorf("failed to extract replicas from %s: %w", gvk, err)
		}
	}

	return &genericStatefulWorkload{
		object:         obj,
		namespacedName: namespacedName,
		replicas:       replicas,
		gvk:            gvk,
	}, nil
}

// extractReplicasFromObject manually extracts replicas from common workload types
func (f *WorkloadFactory) extractReplicasFromObject(obj *unstructured.Unstructured) (int32, error) {
	gvk := obj.GroupVersionKind()
	
	// Common paths for replicas field in different workload types
	replicasPaths := [][]string{
		{"spec", "replicas"},           // Standard Kubernetes workloads
		{"spec", "replicas"},           // Kruise workloads
		{"spec", "pods"},               // StrimziPodSet
		{"spec", "size"},               // Some custom workloads
		{"spec", "instances"},          // Some custom workloads
	}
	
	for _, path := range replicasPaths {
		if replicas, found, err := unstructured.NestedInt64(obj.Object, path...); err == nil && found {
			klog.V(4).Infof("Found replicas %d at path %v for %s", replicas, path, gvk)
			return int32(replicas), nil
		}
	}
	
	// Default to 1 if no replicas field found (like Pod)
	klog.V(4).Infof("No replicas field found for %s, defaulting to 1", gvk)
	return 1, nil
}

// genericStatefulWorkload is a generic implementation of StatefulWorkload
type genericStatefulWorkload struct {
	object         *unstructured.Unstructured
	namespacedName types.NamespacedName
	replicas       int32
	gvk            schema.GroupVersionKind
}

// GetReplicas returns the desired number of replicas
func (w *genericStatefulWorkload) GetReplicas() int32 {
	return w.replicas
}

// GetGVK returns the GroupVersionKind of the workload
func (w *genericStatefulWorkload) GetGVK() schema.GroupVersionKind {
	return w.gvk
}

// GetNamespacedName returns the namespaced name of the workload
func (w *genericStatefulWorkload) GetNamespacedName() types.NamespacedName {
	return w.namespacedName
}

// GetObject returns the underlying unstructured object
func (w *genericStatefulWorkload) GetObject() *unstructured.Unstructured {
	return w.object
}

// SupportedWorkloadTypes returns a list of supported stateful workload types
func SupportedWorkloadTypes() []schema.GroupVersionKind {
	return []schema.GroupVersionKind{
		// Standard Kubernetes
		{Group: "apps", Version: "v1", Kind: "StatefulSet"},
		
		// Kruise workloads
		{Group: "apps.kruise.io", Version: "v1alpha1", Kind: "StatefulSet"},
		{Group: "apps.kruise.io", Version: "v1beta1", Kind: "StatefulSet"},
		{Group: "apps.kruise.io", Version: "v1", Kind: "StatefulSet"},
		{Group: "apps.kruise.io", Version: "v1alpha1", Kind: "CloneSet"},
		{Group: "apps.kruise.io", Version: "v1alpha1", Kind: "AdvancedStatefulSet"},
		
		// Strimzi workloads
		{Group: "core.strimzi.io", Version: "v1beta2", Kind: "StrimziPodSet"},
		{Group: "core.strimzi.io", Version: "v1beta1", Kind: "StrimziPodSet"},
		
		// Other common stateful workloads
		{Group: "postgresql.cnpg.io", Version: "v1", Kind: "Cluster"},
		{Group: "mysql.oracle.com", Version: "v2", Kind: "InnoDBCluster"},
		{Group: "redis.redis.opstreelabs.in", Version: "v1beta1", Kind: "Redis"},
		{Group: "redis.redis.opstreelabs.in", Version: "v1beta1", Kind: "RedisCluster"},
		
		// Custom workloads (examples)
		{Group: "workload.example.io", Version: "v1alpha1", Kind: "Workload"},
	}
}

// IsStatefulWorkload checks if a GVK represents a supported stateful workload
func IsStatefulWorkload(gvk schema.GroupVersionKind) bool {
	supported := SupportedWorkloadTypes()
	for _, supportedGVK := range supported {
		if gvk.Group == supportedGVK.Group && gvk.Kind == supportedGVK.Kind {
			// Allow any version within the same group/kind
			return true
		}
	}
	return false
}

// GetWorkloadDisplayName returns a human-readable name for the workload type
func GetWorkloadDisplayName(gvk schema.GroupVersionKind) string {
	switch {
	case gvk.Group == "apps" && gvk.Kind == "StatefulSet":
		return "Kubernetes StatefulSet"
	case gvk.Group == "apps.kruise.io" && gvk.Kind == "StatefulSet":
		return "Kruise StatefulSet"
	case gvk.Group == "apps.kruise.io" && gvk.Kind == "CloneSet":
		return "Kruise CloneSet"
	case gvk.Group == "apps.kruise.io" && gvk.Kind == "AdvancedStatefulSet":
		return "Kruise AdvancedStatefulSet"
	case gvk.Group == "core.strimzi.io" && gvk.Kind == "StrimziPodSet":
		return "Strimzi PodSet"
	case gvk.Group == "postgresql.cnpg.io" && gvk.Kind == "Cluster":
		return "PostgreSQL Cluster"
	case gvk.Group == "mysql.oracle.com" && gvk.Kind == "InnoDBCluster":
		return "MySQL InnoDB Cluster"
	case gvk.Group == "redis.redis.opstreelabs.in":
		return fmt.Sprintf("Redis %s", gvk.Kind)
	default:
		return fmt.Sprintf("%s/%s", gvk.Group, gvk.Kind)
	}
}
