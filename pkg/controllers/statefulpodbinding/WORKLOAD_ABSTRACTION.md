# StatefulPodBinding 工作负载抽象层

## 概述

StatefulPodBinding 控制器现在支持多种有状态工作负载类型，不再局限于标准的 Kubernetes StatefulSet。通过工作负载抽象层，控制器可以处理各种第三方有状态工作负载，包括 Kruise、Strimzi 等。

## 支持的工作负载类型

### 标准 Kubernetes 工作负载
- **StatefulSet** (`apps/v1`)

### Kruise 工作负载
- **StatefulSet** (`apps.kruise.io/v1alpha1`, `apps.kruise.io/v1beta1`, `apps.kruise.io/v1`)
- **CloneSet** (`apps.kruise.io/v1alpha1`)
- **AdvancedStatefulSet** (`apps.kruise.io/v1alpha1`)

### Strimzi 工作负载
- **StrimziPodSet** (`core.strimzi.io/v1beta1`, `core.strimzi.io/v1beta2`)

### 数据库工作负载
- **PostgreSQL Cluster** (`postgresql.cnpg.io/v1`)
- **MySQL InnoDB Cluster** (`mysql.oracle.com/v2`)
- **Redis** (`redis.redis.opstreelabs.in/v1beta1`)
- **RedisCluster** (`redis.redis.opstreelabs.in/v1beta1`)

### 自定义工作负载
- **Workload** (`workload.example.io/v1alpha1`) - 示例自定义工作负载

## 架构设计

### 工作负载抽象接口

```go
type StatefulWorkload interface {
    // GetReplicas 返回期望的副本数
    GetReplicas() int32
    
    // GetGVK 返回工作负载的 GroupVersionKind
    GetGVK() schema.GroupVersionKind
    
    // GetNamespacedName 返回工作负载的命名空间名称
    GetNamespacedName() types.NamespacedName
    
    // GetObject 返回底层的 unstructured 对象
    GetObject() *unstructured.Unstructured
}
```

### 工作负载工厂

```go
type WorkloadFactory struct {
    client              client.Client
    resourceInterpreter resourceinterpreter.ResourceInterpreter
}
```

工作负载工厂负责：
1. 从 StatefulPodBinding 引用中获取工作负载对象
2. 使用资源解释器提取副本数信息
3. 创建统一的工作负载抽象

### 副本数提取策略

1. **优先使用资源解释器**：利用 Karmada 的资源解释器框架
2. **回退到手动提取**：支持常见的副本数字段路径
3. **默认值处理**：对于没有副本数概念的工作负载默认为 1

## 使用示例

### Kruise StatefulSet

```yaml
apiVersion: work.karmada.io/v1alpha2
kind: StatefulPodBinding
metadata:
  name: kruise-statefulset-binding
  namespace: default
spec:
  resource:
    apiVersion: apps.kruise.io/v1alpha1
    kind: StatefulSet
    name: my-kruise-statefulset
    namespace: default
  replicas: 5
  placement:
    replicaScheduling:
      statefulFixedPodPlacement:
      - clusterName: cluster1
        podIndexes: [0, 1]  # 主节点
      - clusterName: cluster2
        podIndexes: [4]     # 特殊节点
      weightPreference:
        staticWeightList:
        - targetCluster:
            clusterNames: ["cluster1"]
          weight: 2
        - targetCluster:
            clusterNames: ["cluster2"]
          weight: 1
```

### StrimziPodSet

```yaml
apiVersion: work.karmada.io/v1alpha2
kind: StatefulPodBinding
metadata:
  name: strimzi-podset-binding
  namespace: kafka
spec:
  resource:
    apiVersion: core.strimzi.io/v1beta2
    kind: StrimziPodSet
    name: my-kafka-cluster
    namespace: kafka
  replicas: 3
  placement:
    replicaScheduling:
      statefulFixedPodPlacement:
      - clusterName: cluster1
        podIndexes: [0]     # Kafka broker 0
      - clusterName: cluster2
        podIndexes: [1]     # Kafka broker 1
      - clusterName: cluster3
        podIndexes: [2]     # Kafka broker 2
```

### PostgreSQL Cluster

```yaml
apiVersion: work.karmada.io/v1alpha2
kind: StatefulPodBinding
metadata:
  name: postgres-cluster-binding
  namespace: database
spec:
  resource:
    apiVersion: postgresql.cnpg.io/v1
    kind: Cluster
    name: my-postgres-cluster
    namespace: database
  replicas: 3
  placement:
    replicaScheduling:
      statefulFixedPodPlacement:
      - clusterName: cluster1
        podIndexes: [0]     # Primary
      - clusterName: cluster2
        podIndexes: [1, 2]  # Replicas
```

## 扩展支持新的工作负载类型

### 1. 添加到支持列表

在 `workload.go` 的 `SupportedWorkloadTypes()` 函数中添加新的 GVK：

```go
func SupportedWorkloadTypes() []schema.GroupVersionKind {
    return []schema.GroupVersionKind{
        // ... 现有类型
        {Group: "myoperator.io", Version: "v1", Kind: "MyStatefulWorkload"},
    }
}
```

### 2. 添加显示名称

在 `GetWorkloadDisplayName()` 函数中添加友好的显示名称：

```go
case gvk.Group == "myoperator.io" && gvk.Kind == "MyStatefulWorkload":
    return "My Custom Stateful Workload"
```

### 3. 配置资源解释器（可选）

如果工作负载使用非标准的副本数字段，可以：

1. **创建自定义资源解释器**：实现 `ResourceInterpreter` 接口
2. **使用 Lua 脚本**：通过 `ResourceInterpreterCustomization` 配置
3. **添加到回退路径**：在 `extractReplicasFromObject()` 中添加字段路径

### 4. 测试新工作负载

```go
func TestNewWorkloadType(t *testing.T) {
    // 创建测试工作负载对象
    workload := &unstructured.Unstructured{}
    workload.SetGroupVersionKind(schema.GroupVersionKind{
        Group:   "myoperator.io",
        Version: "v1",
        Kind:    "MyStatefulWorkload",
    })
    
    // 测试工作负载抽象
    factory := NewWorkloadFactory(client, interpreter)
    statefulWorkload, err := factory.createWorkload(workload, namespacedName)
    // ... 验证逻辑
}
```

## 最佳实践

### 1. 资源解释器配置

对于复杂的自定义工作负载，建议使用 Karmada 的资源解释器框架：

```yaml
apiVersion: config.karmada.io/v1alpha1
kind: ResourceInterpreterCustomization
metadata:
  name: my-workload-interpreter
spec:
  target:
    apiVersion: myoperator.io/v1
    kind: MyStatefulWorkload
  customizations:
    replicaResource:
      luaScript: |
        function GetReplicas(obj)
          return obj.spec.instances or 1
        end
```

### 2. 控制器初始化

确保在控制器启动时正确初始化资源解释器：

```go
controller := &Controller{
    Client:              client,
    ResourceInterpreter: resourceInterpreter,
    // WorkloadFactory 会在需要时自动初始化
}
```

### 3. 错误处理

工作负载抽象层提供了健壮的错误处理：
- 自动回退到手动副本数提取
- 对未知工作负载类型提供默认值
- 详细的日志记录用于故障排除

## 性能考虑

1. **延迟初始化**：WorkloadFactory 在首次使用时才初始化
2. **缓存机制**：资源解释器内部使用缓存提高性能
3. **批量操作**：支持批量处理多个工作负载

## 故障排除

### 常见问题

1. **不支持的工作负载类型**
   - 检查 `SupportedWorkloadTypes()` 列表
   - 验证 GVK 是否正确

2. **副本数提取失败**
   - 检查资源解释器配置
   - 验证工作负载对象的字段结构

3. **控制器无法监听工作负载变化**
   - 当前版本仅监听 StatefulPodBinding 变化
   - 计划在未来版本中添加动态工作负载监听

### 调试日志

启用详细日志以获取更多信息：

```bash
--v=4  # 显示工作负载类型检测信息
--v=5  # 显示副本数提取详情
```

## 未来计划

1. **动态工作负载监听**：基于集群中可用的 CRD 动态设置监听
2. **高级亲和性支持**：支持标签选择器等高级调度特性
3. **健康状态感知**：基于工作负载健康状态进行调度决策
4. **Pod 迁移支持**：支持受控的 Pod 在集群间迁移
