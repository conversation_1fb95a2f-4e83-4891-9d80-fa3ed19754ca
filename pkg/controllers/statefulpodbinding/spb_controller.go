package statefulpodbinding

import (
	"context"
	"fmt"
	"sort"

	corev1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/tools/record"
	"k8s.io/klog/v2"
	controllerruntime "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"

	clusterv1alpha1 "github.com/karmada-io/karmada/pkg/apis/cluster/v1alpha1"
	policyv1alpha1 "github.com/karmada-io/karmada/pkg/apis/policy/v1alpha1"
	workv1alpha2 "github.com/karmada-io/karmada/pkg/apis/work/v1alpha2"
	"github.com/karmada-io/karmada/pkg/resourceinterpreter"
	schedulercache "github.com/karmada-io/karmada/pkg/scheduler/cache"
	"github.com/karmada-io/karmada/pkg/scheduler/core"
	"github.com/karmada-io/karmada/pkg/sharedcli/ratelimiterflag"
	"github.com/karmada-io/karmada/pkg/util"
)

// ControllerName is the controller name that will be used when reporting events.
const ControllerName = "stateful-pod-binding-controller"

// Controller reconciles StatefulPodBinding objects
type Controller struct {
	client.Client
	Scheme                      *runtime.Scheme
	EventRecorder               record.EventRecorder
	RateLimiterOptions          ratelimiterflag.Options
	ClusterClientSetFunc        func(clusterName string, client client.Client) (*util.ClusterClient, error)
	ClusterDynamicClientSetFunc func(clusterName string, client client.Client) (*util.DynamicClusterClient, error)

	Algorithm           core.ScheduleAlgorithm
	schedulerCache      schedulercache.Cache
	ResourceInterpreter resourceinterpreter.ResourceInterpreter
	WorkloadFactory     *WorkloadFactory
}

// Reconcile performs a full reconciliation for the object referred to by the Request.
func (c *Controller) Reconcile(ctx context.Context, req controllerruntime.Request) (controllerruntime.Result, error) {
	klog.V(4).Infof("Reconciling StatefulPodBinding %s", req.NamespacedName)

	// Fetch the StatefulPodBinding instance
	spb := &workv1alpha2.StatefulPodBinding{}
	if err := c.Get(ctx, req.NamespacedName, spb); err != nil {
		if apierrors.IsNotFound(err) {
			// Object not found, return.  Created objects are automatically garbage collected.
			return controllerruntime.Result{}, nil
		}
		klog.Errorf("Failed to get StatefulPodBinding %s: %v", req.NamespacedName, err)
		return controllerruntime.Result{}, err
	}
	// Schedule pods to clusters based on placement strategy
	scheduledClusters, err := c.schedulePods(ctx)
	if err != nil {
		klog.Errorf("Failed to schedule pods for StatefulPodBinding %s/%s: %v", spb.Namespace, spb.Name, err)
		return controllerruntime.Result{Requeue: true}, err
	}

	// Update StatefulPodBinding spec with scheduled clusters
	return c.updateStatefulPodBinding(ctx, spb, scheduledClusters)
}

// getAvailableClusters gets the list of available clusters for scheduling
func (c *Controller) getAvailableClusters(ctx context.Context, spb *workv1alpha2.StatefulPodBinding) ([]*clusterv1alpha1.Cluster, error) {
	clusterList := &clusterv1alpha1.ClusterList{}
	if err := c.Client.List(ctx, clusterList); err != nil {
		return nil, fmt.Errorf("failed to list clusters: %w", err)
	}

	var clusters []*clusterv1alpha1.Cluster
	for i := range clusterList.Items {
		cluster := &clusterList.Items[i]
		// Filter clusters based on placement constraints if any
		if c.isClusterEligible(cluster, spb.Spec.Placement) {
			clusters = append(clusters, cluster)
		}
	}

	return clusters, nil
}

// isClusterEligible checks if a cluster is eligible based on placement constraints
func (c *Controller) isClusterEligible(cluster *clusterv1alpha1.Cluster, placement *policyv1alpha1.Placement) bool {
	if placement == nil {
		return true
	}

	// Check cluster affinity
	if placement.ClusterAffinity != nil {
		if len(placement.ClusterAffinity.ClusterNames) > 0 {
			for _, name := range placement.ClusterAffinity.ClusterNames {
				if cluster.Name == name {
					return true
				}
			}
			return false
		}
	}

	// TODO: Add more placement constraint checks (labels, taints, etc.)
	return true
}

// getCurrentPodDistribution 获取当前副本数 每个索引所在的 member
func (c *Controller) getCurrentPodDistribution(ctx context.Context, spb *workv1alpha2.StatefulPodBinding) ([]string, error) {
	pods, err := c.listPods(ctx, spb)
	if err != nil {
		return nil, err
	}
	replicaCount := int(spb.Spec.Replicas)

	replicas := make([]string, replicaCount)
	for i := range pods {
		if ord := getOrdinal(pods[i]); 0 <= ord && ord < replicaCount {
			// if the ordinal of the pod is within the range of the current number of replicas and not in reserveOrdinals,
			// insert it at the indirection of its ordinal
			replicas[ord] = getMemberCluster(pods[i])
		}
	}
	return replicas, nil
}

// listPods gets the current pod for the stateful resource
func (c *Controller) listPods(ctx context.Context, spb *workv1alpha2.StatefulPodBinding) ([]*corev1.Pod, error) {
	selector := spb.Spec.PodSelector
	if selector == nil {
		return nil, fmt.Errorf("pod selector is nil")
	}

	ls, err := metav1.LabelSelectorAsSelector(selector)
	if err != nil {
		return nil, fmt.Errorf("failed to convert label selector: %w", err)
	}

	podList := &corev1.PodList{}
	if err := c.Client.List(ctx, podList, client.InNamespace(spb.Namespace), client.MatchingLabelsSelector{Selector: ls}); err != nil {
		return nil, fmt.Errorf("failed to list pods: %w", err)
	}

	var ret []*corev1.Pod
	for i := range podList.Items {
		p := &podList.Items[i]
		if isMemberOf(spb.Spec.Resource.Name, p) {
			ret = append(ret, p)
		}
	}
	return ret, nil
}

// schedulePodsIndices
// 基于 targets 中的member 集群的副本数，将索引后缀分配到不同的member集群上，尽可能的符合上一步的副本数量，并且使得受影响的pod数量最小
// placement.ReplicaScheduling.StatefulFixedPodPlacement 配置优先级最高，剩余的按副本数分配
func (c *Controller) schedulePodsIndices(ctx context.Context, spb *workv1alpha2.StatefulPodBinding, currentPodsMember []string, targets []workv1alpha2.TargetCluster) ([]workv1alpha2.TargetCluster, error) {
	totalReplicas := spb.Spec.Replicas
	if totalReplicas == 0 {
		return targets, nil
	}

	// 初始化结果，复制 targets 以避免修改原始数据
	result := make([]workv1alpha2.TargetCluster, len(targets))
	for i, target := range targets {
		result[i] = workv1alpha2.TargetCluster{
			Name:     target.Name,
			Replicas: target.Replicas,
		}
	}

	// 创建集群名称到索引的映射
	clusterIndexMap := make(map[string]int)
	for i, target := range result {
		clusterIndexMap[target.Name] = i
	}

	// 跟踪已分配的 Pod 索引
	assignedIndices := make(map[int32]bool)

	// 第一步：处理 StatefulFixedPodPlacement（最高优先级）
	if spb.Spec.Placement != nil &&
		spb.Spec.Placement.ReplicaScheduling != nil &&
		len(spb.Spec.Placement.ReplicaScheduling.StatefulFixedPodPlacement) > 0 {

		for _, fixedPlacement := range spb.Spec.Placement.ReplicaScheduling.StatefulFixedPodPlacement {
			clusterIdx, exists := clusterIndexMap[fixedPlacement.ClusterName]
			if !exists {
				klog.Warningf("Fixed placement references non-existent cluster: %s", fixedPlacement.ClusterName)
				continue
			}

			// 验证并分配固定的 Pod 索引
			validIndices := make([]int32, 0, len(fixedPlacement.PodIndexes))
			for _, podIndex := range fixedPlacement.PodIndexes {
				if podIndex >= 0 && podIndex < totalReplicas && !assignedIndices[podIndex] {
					validIndices = append(validIndices, podIndex)
					assignedIndices[podIndex] = true
				} else {
					klog.Warningf("Invalid or duplicate pod index %d for cluster %s", podIndex, fixedPlacement.ClusterName)
				}
			}

			// 更新目标集群的 Pod 索引
			if len(validIndices) > 0 {
				result[clusterIdx].PodIndices = validIndices
				klog.V(4).Infof("Assigned fixed pod indices %v to cluster %s", validIndices, fixedPlacement.ClusterName)
			}
		}
	}

	// 第二步：收集未分配的 Pod 索引
	unassignedIndices := make([]int32, 0)
	for i := int32(0); i < totalReplicas; i++ {
		if !assignedIndices[i] {
			unassignedIndices = append(unassignedIndices, i)
		}
	}

	// 第三步：按副本数分配剩余的 Pod 索引
	if len(unassignedIndices) > 0 {
		// 计算每个集群需要的额外副本数（减去已通过固定放置分配的）
		clusterNeeds := make([]struct {
			clusterIdx int
			needed     int32
			current    int32
		}, 0, len(result))

		for i, target := range result {
			currentAssigned := int32(len(target.PodIndices))
			needed := target.Replicas - currentAssigned
			if needed > 0 {
				clusterNeeds = append(clusterNeeds, struct {
					clusterIdx int
					needed     int32
					current    int32
				}{
					clusterIdx: i,
					needed:     needed,
					current:    currentAssigned,
				})
			}
		}

		// 第一轮：优先保持当前在该集群的 Pod
		for _, need := range clusterNeeds {
			clusterName := result[need.clusterIdx].Name

			// 找到当前已经在这个集群的未分配 Pod
			var toKeep []int32
			for i := len(unassignedIndices) - 1; i >= 0; i-- {
				podIndex := unassignedIndices[i]
				currentCluster := ""
				if int(podIndex) < len(currentPodsMember) {
					currentCluster = currentPodsMember[podIndex]
				}

				if currentCluster == clusterName {
					toKeep = append(toKeep, podIndex)
					// 从未分配列表中移除
					unassignedIndices = append(unassignedIndices[:i], unassignedIndices[i+1:]...)
				}
			}

			// 按需要的数量保留 Pod（不超过需要的数量）
			keepCount := int32(len(toKeep))
			if keepCount > need.needed {
				keepCount = need.needed
			}

			// 添加要保留的 Pod 索引
			for i := int32(0); i < keepCount; i++ {
				result[need.clusterIdx].PodIndices = append(result[need.clusterIdx].PodIndices, toKeep[i])
			}

			// 如果保留的 Pod 超过需要的数量，将多余的放回未分配列表
			for i := keepCount; i < int32(len(toKeep)); i++ {
				unassignedIndices = append(unassignedIndices, toKeep[i])
			}

			klog.V(4).Infof("Cluster %s: kept %d existing pods", clusterName, keepCount)
		}

		// 第二轮：为仍需要更多副本的集群分配剩余的 Pod
		unassignedIdx := 0
		for _, need := range clusterNeeds {
			currentAssigned := int32(len(result[need.clusterIdx].PodIndices))
			stillNeeded := need.needed - currentAssigned

			if stillNeeded > 0 {
				assigned := int32(0)
				for assigned < stillNeeded && unassignedIdx < len(unassignedIndices) {
					result[need.clusterIdx].PodIndices = append(result[need.clusterIdx].PodIndices, unassignedIndices[unassignedIdx])
					assigned++
					unassignedIdx++
				}

				klog.V(4).Infof("Cluster %s: assigned %d additional pods (total: %d)",
					result[need.clusterIdx].Name, assigned, len(result[need.clusterIdx].PodIndices))
			}
		}
	}

	// 第四步：对每个集群的 Pod 索引进行排序
	for i := range result {
		sort.Slice(result[i].PodIndices, func(a, b int) bool {
			return result[i].PodIndices[a] < result[i].PodIndices[b]
		})
	}

	// 验证分配结果
	if err := c.validatePodIndexAssignment(result, totalReplicas); err != nil {
		return nil, fmt.Errorf("pod index assignment validation failed: %w", err)
	}

	return result, nil
}

// removeIndex removes the first occurrence of the specified index from the slice
func removeIndex(slice []int32, index int32) []int32 {
	for i, v := range slice {
		if v == index {
			return append(slice[:i], slice[i+1:]...)
		}
	}
	return slice
}

// validatePodIndexAssignment validates that all pod indices are assigned correctly
func (c *Controller) validatePodIndexAssignment(targets []workv1alpha2.TargetCluster, totalReplicas int32) error {
	assignedIndices := make(map[int32]bool)
	totalAssigned := int32(0)

	for _, target := range targets {
		for _, podIndex := range target.PodIndices {
			if podIndex < 0 || podIndex >= totalReplicas {
				return fmt.Errorf("invalid pod index %d for cluster %s (must be 0-%d)",
					podIndex, target.Name, totalReplicas-1)
			}

			if assignedIndices[podIndex] {
				return fmt.Errorf("duplicate pod index %d assigned to cluster %s",
					podIndex, target.Name)
			}

			assignedIndices[podIndex] = true
			totalAssigned++
		}
	}

	if totalAssigned != totalReplicas {
		return fmt.Errorf("total assigned pods (%d) does not match expected replicas (%d)",
			totalAssigned, totalReplicas)
	}

	return nil
}

// schedulePods schedules pods to clusters based on placement strategy
func (c *Controller) schedulePods(ctx context.Context) ([]workv1alpha2.TargetCluster, error) {
	return nil, nil
}

// applyPlacementStrategy applies the placement strategy to schedule pods
func (c *Controller) applyPlacementStrategy(placement *policyv1alpha1.Placement, replicas int32, clusterPodMap map[string][]int32, clusters []*clusterv1alpha1.Cluster) error {
	// Handle fixed pod placement first (highest priority)
	if placement.ReplicaScheduling != nil && len(placement.ReplicaScheduling.StatefulFixedPodPlacement) > 0 {
		for _, fixedPlacement := range placement.ReplicaScheduling.StatefulFixedPodPlacement {
			for _, podIndex := range fixedPlacement.PodIndexes {
				if podIndex >= replicas {
					continue // Skip invalid pod indices
				}

				// Remove pod from other clusters if it exists
				c.removePodFromOtherClusters(clusterPodMap, fixedPlacement.ClusterName, podIndex)

				// Add pod to the specified cluster
				if clusterPodMap[fixedPlacement.ClusterName] == nil {
					clusterPodMap[fixedPlacement.ClusterName] = []int32{}
				}
				if !c.containsPodIndex(clusterPodMap[fixedPlacement.ClusterName], podIndex) {
					clusterPodMap[fixedPlacement.ClusterName] = append(clusterPodMap[fixedPlacement.ClusterName], podIndex)
				}
			}
		}
	}

	// Handle weighted placement for remaining pods
	if placement.ReplicaScheduling != nil && placement.ReplicaScheduling.WeightPreference != nil {
		return c.applyWeightedPlacement(placement, replicas, clusterPodMap, clusters)
	}

	// Default: distribute remaining pods evenly
	return c.distributeRemainingPodsEvenly(replicas, clusterPodMap, clusters)
}

// removePodFromOtherClusters removes a pod index from all clusters except the specified one
func (c *Controller) removePodFromOtherClusters(clusterPodMap map[string][]int32, targetCluster string, podIndex int32) {
	for clusterName, podIndices := range clusterPodMap {
		if clusterName != targetCluster {
			for i, idx := range podIndices {
				if idx == podIndex {
					clusterPodMap[clusterName] = append(podIndices[:i], podIndices[i+1:]...)
					break
				}
			}
		}
	}
}

// containsPodIndex checks if a pod index exists in the slice
func (c *Controller) containsPodIndex(podIndices []int32, podIndex int32) bool {
	for _, idx := range podIndices {
		if idx == podIndex {
			return true
		}
	}
	return false
}

// applyWeightedPlacement applies weighted placement strategy
func (c *Controller) applyWeightedPlacement(placement *policyv1alpha1.Placement, replicas int32, clusterPodMap map[string][]int32, clusters []*clusterv1alpha1.Cluster) error {
	// Get assigned pods (from fixed placement)
	assignedPods := make(map[int32]bool)
	for _, podIndices := range clusterPodMap {
		for _, podIndex := range podIndices {
			assignedPods[podIndex] = true
		}
	}

	// Get unassigned pods
	var unassignedPods []int32
	for i := int32(0); i < replicas; i++ {
		if !assignedPods[i] {
			unassignedPods = append(unassignedPods, i)
		}
	}

	if len(unassignedPods) == 0 {
		return nil // All pods are already assigned
	}

	// Calculate cluster weights
	clusterWeights := c.calculateClusterWeights(placement.ReplicaScheduling.WeightPreference, clusters)

	// Distribute unassigned pods based on weights
	return c.distributePodsBasedOnWeights(unassignedPods, clusterPodMap, clusterWeights)
}

// calculateClusterWeights calculates weights for each cluster
func (c *Controller) calculateClusterWeights(weightPreference *policyv1alpha1.ClusterPreferences, clusters []*clusterv1alpha1.Cluster) map[string]int64 {
	weights := make(map[string]int64)

	if weightPreference.StaticWeightList != nil {
		for _, weight := range weightPreference.StaticWeightList {
			for _, cluster := range clusters {
				if c.clusterMatchesAffinity(cluster, &weight.TargetCluster) {
					weights[cluster.Name] = weight.Weight
				}
			}
		}
	}

	// Set default weight for clusters without explicit weight
	for _, cluster := range clusters {
		if _, exists := weights[cluster.Name]; !exists {
			weights[cluster.Name] = 1
		}
	}

	return weights
}

// clusterMatchesAffinity checks if a cluster matches the cluster affinity
func (c *Controller) clusterMatchesAffinity(cluster *clusterv1alpha1.Cluster, affinity *policyv1alpha1.ClusterAffinity) bool {
	if len(affinity.ClusterNames) > 0 {
		for _, name := range affinity.ClusterNames {
			if cluster.Name == name {
				return true
			}
		}
		return false
	}
	// TODO: Add label selector matching
	return true
}

// distributePodsBasedOnWeights distributes pods based on cluster weights
func (c *Controller) distributePodsBasedOnWeights(unassignedPods []int32, clusterPodMap map[string][]int32, clusterWeights map[string]int64) error {
	if len(unassignedPods) == 0 || len(clusterWeights) == 0 {
		return nil
	}

	// Calculate total weight
	var totalWeight int64
	var clusterNames []string
	for clusterName, weight := range clusterWeights {
		totalWeight += weight
		clusterNames = append(clusterNames, clusterName)
	}
	sort.Strings(clusterNames) // For consistent ordering

	// Distribute pods proportionally
	podIndex := 0
	for _, podIdx := range unassignedPods {
		// Use round-robin with weight consideration
		targetCluster := clusterNames[podIndex%len(clusterNames)]

		if clusterPodMap[targetCluster] == nil {
			clusterPodMap[targetCluster] = []int32{}
		}
		clusterPodMap[targetCluster] = append(clusterPodMap[targetCluster], podIdx)
		podIndex++
	}

	return nil
}

// distributeRemainingPodsEvenly distributes remaining pods evenly across clusters
func (c *Controller) distributeRemainingPodsEvenly(replicas int32, clusterPodMap map[string][]int32, clusters []*clusterv1alpha1.Cluster) error {
	if len(clusters) == 0 {
		return fmt.Errorf("no clusters available for scheduling")
	}

	// Get assigned pods
	assignedPods := make(map[int32]bool)
	for _, podIndices := range clusterPodMap {
		for _, podIndex := range podIndices {
			assignedPods[podIndex] = true
		}
	}

	// Get unassigned pods
	var unassignedPods []int32
	for i := int32(0); i < replicas; i++ {
		if !assignedPods[i] {
			unassignedPods = append(unassignedPods, i)
		}
	}

	if len(unassignedPods) == 0 {
		return nil // All pods are already assigned
	}

	// Distribute unassigned pods evenly
	clusterIndex := 0
	for _, podIdx := range unassignedPods {
		clusterName := clusters[clusterIndex%len(clusters)].Name

		if clusterPodMap[clusterName] == nil {
			clusterPodMap[clusterName] = []int32{}
		}
		clusterPodMap[clusterName] = append(clusterPodMap[clusterName], podIdx)
		clusterIndex++
	}

	return nil
}

// updateStatefulPodBinding updates the StatefulPodBinding with scheduled clusters
func (c *Controller) updateStatefulPodBinding(ctx context.Context, spb *workv1alpha2.StatefulPodBinding, scheduledClusters []workv1alpha2.TargetCluster) (controllerruntime.Result, error) {
	// Check if update is needed
	if c.clustersEqual(spb.Spec.Clusters, scheduledClusters) {
		klog.V(4).Infof("StatefulPodBinding %s/%s clusters unchanged, skipping update", spb.Namespace, spb.Name)
		return controllerruntime.Result{}, nil
	}

	// Update the spec
	spb.Spec.Clusters = scheduledClusters

	if err := c.Client.Update(ctx, spb); err != nil {
		klog.Errorf("Failed to update StatefulPodBinding %s/%s: %v", spb.Namespace, spb.Name, err)
		return controllerruntime.Result{Requeue: true}, err
	}

	klog.Infof("Successfully updated StatefulPodBinding %s/%s with %d clusters", spb.Namespace, spb.Name, len(scheduledClusters))
	return controllerruntime.Result{}, nil
}

// clustersEqual checks if two cluster lists are equal
func (c *Controller) clustersEqual(current, new []workv1alpha2.TargetCluster) bool {
	if len(current) != len(new) {
		return false
	}

	// Create maps for comparison
	currentMap := make(map[string][]int32)
	for _, cluster := range current {
		currentMap[cluster.Name] = cluster.PodIndices
	}

	newMap := make(map[string][]int32)
	for _, cluster := range new {
		newMap[cluster.Name] = cluster.PodIndices
	}

	// Compare maps
	for clusterName, currentIndices := range currentMap {
		newIndices, exists := newMap[clusterName]
		if !exists || !c.slicesEqual(currentIndices, newIndices) {
			return false
		}
	}

	for clusterName := range newMap {
		if _, exists := currentMap[clusterName]; !exists {
			return false
		}
	}

	return true
}

// slicesEqual checks if two int32 slices are equal
func (c *Controller) slicesEqual(a, b []int32) bool {
	if len(a) != len(b) {
		return false
	}

	// Sort both slices for comparison
	aCopy := make([]int32, len(a))
	bCopy := make([]int32, len(b))
	copy(aCopy, a)
	copy(bCopy, b)

	sort.Slice(aCopy, func(i, j int) bool { return aCopy[i] < aCopy[j] })
	sort.Slice(bCopy, func(i, j int) bool { return bCopy[i] < bCopy[j] })

	for i := range aCopy {
		if aCopy[i] != bCopy[i] {
			return false
		}
	}

	return true
}

// SetupWithManager sets up the controller with the Manager.
func (c *Controller) SetupWithManager(mgr controllerruntime.Manager) error {
	// For now, we only watch StatefulPodBinding resources directly
	// TODO: Add dynamic watching for workload types based on available CRDs
	return controllerruntime.NewControllerManagedBy(mgr).
		For(&workv1alpha2.StatefulPodBinding{}).
		WithOptions(controller.Options{
			RateLimiter: ratelimiterflag.DefaultControllerRateLimiter(c.RateLimiterOptions),
		}).
		Complete(c)
}

// findStatefulPodBindingsForWorkload finds StatefulPodBindings that reference any supported workload
func (c *Controller) findStatefulPodBindingsForWorkload(obj client.Object) []reconcile.Request {
	// Get the GVK of the workload
	gvk := obj.GetObjectKind().GroupVersionKind()

	// Check if this is a supported stateful workload type
	if !IsStatefulWorkload(gvk) {
		klog.V(4).Infof("Ignoring unsupported workload type: %s", gvk)
		return nil
	}

	spbList := &workv1alpha2.StatefulPodBindingList{}
	if err := c.Client.List(context.TODO(), spbList, client.InNamespace(obj.GetNamespace())); err != nil {
		klog.Errorf("Failed to list StatefulPodBindings: %v", err)
		return nil
	}

	var requests []reconcile.Request
	for _, spb := range spbList.Items {
		// Check if this StatefulPodBinding references the workload
		if c.isWorkloadReferenced(&spb, obj, gvk) {
			requests = append(requests, reconcile.Request{
				NamespacedName: types.NamespacedName{
					Namespace: spb.Namespace,
					Name:      spb.Name,
				},
			})
		}
	}

	return requests
}

// isWorkloadReferenced checks if a StatefulPodBinding references the given workload
func (c *Controller) isWorkloadReferenced(spb *workv1alpha2.StatefulPodBinding, workload client.Object, gvk schema.GroupVersionKind) bool {
	if spb.Spec.Resource.Name != workload.GetName() ||
		spb.Spec.Resource.Namespace != workload.GetNamespace() ||
		spb.Spec.Resource.Kind != gvk.Kind {
		return false
	}

	// Check APIVersion match (handle both "v1" and "apps/v1" formats)
	resourceAPIVersion := spb.Spec.Resource.APIVersion
	workloadAPIVersion := gvk.GroupVersion().String()

	return resourceAPIVersion == workloadAPIVersion ||
		resourceAPIVersion == gvk.Version ||
		(gvk.Group == "apps" && resourceAPIVersion == "v1" && gvk.Version == "v1")
}
