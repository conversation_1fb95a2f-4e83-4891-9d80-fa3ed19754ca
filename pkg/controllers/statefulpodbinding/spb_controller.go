package statefulpodbinding

import (
	"context"
	"fmt"
	"sort"
	"time"

	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/tools/record"
	"k8s.io/klog/v2"
	controllerruntime "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller"
	"sigs.k8s.io/controller-runtime/pkg/handler"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"
	"sigs.k8s.io/controller-runtime/pkg/source"

	clusterv1alpha1 "github.com/karmada-io/karmada/pkg/apis/cluster/v1alpha1"
	policyv1alpha1 "github.com/karmada-io/karmada/pkg/apis/policy/v1alpha1"
	workv1alpha2 "github.com/karmada-io/karmada/pkg/apis/work/v1alpha2"
	"github.com/karmada-io/karmada/pkg/scheduler/core"
	schedulercache "github.com/karmada-io/karmada/pkg/scheduler/cache"
	"github.com/karmada-io/karmada/pkg/util"
	"github.com/karmada-io/karmada/pkg/util/helper"
	"github.com/karmada-io/karmada/pkg/util/ratelimiterflag"
)

// ControllerName is the controller name that will be used when reporting events.
const ControllerName = "stateful-pod-binding-controller"

// Controller reconciles StatefulPodBinding objects
type Controller struct {
	client.Client
	Scheme                      *runtime.Scheme
	EventRecorder               record.EventRecorder
	RateLimiterOptions          ratelimiterflag.Options
	ClusterClientSetFunc        func(clusterName string, client client.Client) (*util.ClusterClient, error)
	ClusterDynamicClientSetFunc func(clusterName string, client client.Client) (*util.DynamicClusterClient, error)

	Algorithm      core.ScheduleAlgorithm
	schedulerCache schedulercache.Cache
}

// Reconcile performs a full reconciliation for the object referred to by the Request.
func (c *Controller) Reconcile(ctx context.Context, req controllerruntime.Request) (controllerruntime.Result, error) {
	klog.V(4).Infof("Reconciling StatefulPodBinding %s", req.NamespacedName)

	// Fetch the StatefulPodBinding instance
	spb := &workv1alpha2.StatefulPodBinding{}
	if err := c.Get(ctx, req.NamespacedName, spb); err != nil {
		if apierrors.IsNotFound(err) {
			// Object not found, return.  Created objects are automatically garbage collected.
			return controllerruntime.Result{}, nil
		}
		klog.Errorf("Failed to get StatefulPodBinding %s: %v", req.NamespacedName, err)
		return controllerruntime.Result{}, err
	}

	// Get the referenced workload
	workload, err := c.getStatefulSet(ctx, spb)
	if err != nil {
		klog.Errorf("Failed to get StatefulSet for StatefulPodBinding %s/%s: %v", spb.Namespace, spb.Name, err)
		return controllerruntime.Result{Requeue: true}, err
	}

	// Get available clusters
	clusters, err := c.getAvailableClusters(ctx, spb)
	if err != nil {
		klog.Errorf("Failed to get available clusters for StatefulPodBinding %s/%s: %v", spb.Namespace, spb.Name, err)
		return controllerruntime.Result{Requeue: true}, err
	}

	// Get current pod distribution across clusters
	currentPodDistribution, err := c.getCurrentPodDistribution(ctx, spb, workload)
	if err != nil {
		klog.Errorf("Failed to get current pod distribution for StatefulPodBinding %s/%s: %v", spb.Namespace, spb.Name, err)
		return controllerruntime.Result{Requeue: true}, err
	}

	// Schedule pods to clusters based on placement strategy
	scheduledClusters, err := c.schedulePods(ctx, spb, workload, currentPodDistribution, clusters)
	if err != nil {
		klog.Errorf("Failed to schedule pods for StatefulPodBinding %s/%s: %v", spb.Namespace, spb.Name, err)
		return controllerruntime.Result{Requeue: true}, err
	}

	// Update StatefulPodBinding spec with scheduled clusters
	return c.updateStatefulPodBinding(ctx, spb, scheduledClusters)
}

// getStatefulSet retrieves the StatefulSet referenced by the StatefulPodBinding
func (c *Controller) getStatefulSet(ctx context.Context, spb *workv1alpha2.StatefulPodBinding) (*appsv1.StatefulSet, error) {
	workload := &appsv1.StatefulSet{}
	err := c.Get(ctx, types.NamespacedName{
		Namespace: spb.Spec.Resource.Namespace,
		Name:      spb.Spec.Resource.Name,
	}, workload)
	if err != nil {
		return nil, fmt.Errorf("failed to get StatefulSet %s/%s: %w", spb.Spec.Resource.Namespace, spb.Spec.Resource.Name, err)
	}
	return workload, nil
}

// getAvailableClusters gets the list of available clusters for scheduling
func (c *Controller) getAvailableClusters(ctx context.Context, spb *workv1alpha2.StatefulPodBinding) ([]*clusterv1alpha1.Cluster, error) {
	clusterList := &clusterv1alpha1.ClusterList{}
	if err := c.Client.List(ctx, clusterList); err != nil {
		return nil, fmt.Errorf("failed to list clusters: %w", err)
	}

	var clusters []*clusterv1alpha1.Cluster
	for i := range clusterList.Items {
		cluster := &clusterList.Items[i]
		// Filter clusters based on placement constraints if any
		if c.isClusterEligible(cluster, spb.Spec.Placement) {
			clusters = append(clusters, cluster)
		}
	}

	return clusters, nil
}

// isClusterEligible checks if a cluster is eligible based on placement constraints
func (c *Controller) isClusterEligible(cluster *clusterv1alpha1.Cluster, placement *policyv1alpha1.Placement) bool {
	if placement == nil {
		return true
	}

	// Check cluster affinity
	if placement.ClusterAffinity != nil {
		if len(placement.ClusterAffinity.ClusterNames) > 0 {
			for _, name := range placement.ClusterAffinity.ClusterNames {
				if cluster.Name == name {
					return true
				}
			}
			return false
		}
	}

	// TODO: Add more placement constraint checks (labels, taints, etc.)
	return true
}

// getCurrentPodDistribution gets the current pod distribution across member clusters
func (c *Controller) getCurrentPodDistribution(ctx context.Context, spb *workv1alpha2.StatefulPodBinding, workload *appsv1.StatefulSet) (map[string][]int32, error) {
	distribution := make(map[string][]int32)

	// Get all member clusters
	clusterList := &clusterv1alpha1.ClusterList{}
	if err := c.Client.List(ctx, clusterList); err != nil {
		return nil, fmt.Errorf("failed to list clusters: %w", err)
	}
