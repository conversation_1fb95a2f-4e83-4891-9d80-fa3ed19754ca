package statefulpodbinding

import (
	"context"
	"fmt"
	"sort"

	apierrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/tools/record"
	"k8s.io/klog/v2"
	controllerruntime "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller"
	"sigs.k8s.io/controller-runtime/pkg/handler"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"

	clusterv1alpha1 "github.com/karmada-io/karmada/pkg/apis/cluster/v1alpha1"
	policyv1alpha1 "github.com/karmada-io/karmada/pkg/apis/policy/v1alpha1"
	workv1alpha2 "github.com/karmada-io/karmada/pkg/apis/work/v1alpha2"
	schedulercache "github.com/karmada-io/karmada/pkg/scheduler/cache"
	"github.com/karmada-io/karmada/pkg/scheduler/core"
	"github.com/karmada-io/karmada/pkg/sharedcli/ratelimiterflag"
	"github.com/karmada-io/karmada/pkg/util"
)

// ControllerName is the controller name that will be used when reporting events.
const ControllerName = "stateful-pod-binding-controller"

// Controller reconciles StatefulPodBinding objects
type Controller struct {
	client.Client
	Scheme                      *runtime.Scheme
	EventRecorder               record.EventRecorder
	RateLimiterOptions          ratelimiterflag.Options
	ClusterClientSetFunc        func(clusterName string, client client.Client) (*util.ClusterClient, error)
	ClusterDynamicClientSetFunc func(clusterName string, client client.Client) (*util.DynamicClusterClient, error)

	Algorithm      core.ScheduleAlgorithm
	schedulerCache schedulercache.Cache
}

// Reconcile performs a full reconciliation for the object referred to by the Request.
func (c *Controller) Reconcile(ctx context.Context, req controllerruntime.Request) (controllerruntime.Result, error) {
	klog.V(4).Infof("Reconciling StatefulPodBinding %s", req.NamespacedName)

	// Fetch the StatefulPodBinding instance
	spb := &workv1alpha2.StatefulPodBinding{}
	if err := c.Get(ctx, req.NamespacedName, spb); err != nil {
		if apierrors.IsNotFound(err) {
			// Object not found, return.  Created objects are automatically garbage collected.
			return controllerruntime.Result{}, nil
		}
		klog.Errorf("Failed to get StatefulPodBinding %s: %v", req.NamespacedName, err)
		return controllerruntime.Result{}, err
	}

	// Get the referenced workload
	workload, err := c.getStatefulSet(ctx, spb)
	if err != nil {
		klog.Errorf("Failed to get StatefulSet for StatefulPodBinding %s/%s: %v", spb.Namespace, spb.Name, err)
		return controllerruntime.Result{Requeue: true}, err
	}

	// Get available clusters
	clusters, err := c.getAvailableClusters(ctx, spb)
	if err != nil {
		klog.Errorf("Failed to get available clusters for StatefulPodBinding %s/%s: %v", spb.Namespace, spb.Name, err)
		return controllerruntime.Result{Requeue: true}, err
	}

	// Get current pod distribution across clusters
	currentPodDistribution, err := c.getCurrentPodDistribution(ctx, spb, workload)
	if err != nil {
		klog.Errorf("Failed to get current pod distribution for StatefulPodBinding %s/%s: %v", spb.Namespace, spb.Name, err)
		return controllerruntime.Result{Requeue: true}, err
	}

	// Schedule pods to clusters based on placement strategy
	scheduledClusters, err := c.schedulePods(ctx, spb, workload, currentPodDistribution, clusters)
	if err != nil {
		klog.Errorf("Failed to schedule pods for StatefulPodBinding %s/%s: %v", spb.Namespace, spb.Name, err)
		return controllerruntime.Result{Requeue: true}, err
	}

	// Update StatefulPodBinding spec with scheduled clusters
	return c.updateStatefulPodBinding(ctx, spb, scheduledClusters)
}

// getStatefulSet retrieves the StatefulSet referenced by the StatefulPodBinding
func (c *Controller) getStatefulSet(ctx context.Context, spb *workv1alpha2.StatefulPodBinding) (*appsv1.StatefulSet, error) {
	workload := &appsv1.StatefulSet{}
	err := c.Get(ctx, types.NamespacedName{
		Namespace: spb.Spec.Resource.Namespace,
		Name:      spb.Spec.Resource.Name,
	}, workload)
	if err != nil {
		return nil, fmt.Errorf("failed to get StatefulSet %s/%s: %w", spb.Spec.Resource.Namespace, spb.Spec.Resource.Name, err)
	}
	return workload, nil
}

// getAvailableClusters gets the list of available clusters for scheduling
func (c *Controller) getAvailableClusters(ctx context.Context, spb *workv1alpha2.StatefulPodBinding) ([]*clusterv1alpha1.Cluster, error) {
	clusterList := &clusterv1alpha1.ClusterList{}
	if err := c.Client.List(ctx, clusterList); err != nil {
		return nil, fmt.Errorf("failed to list clusters: %w", err)
	}

	var clusters []*clusterv1alpha1.Cluster
	for i := range clusterList.Items {
		cluster := &clusterList.Items[i]
		// Filter clusters based on placement constraints if any
		if c.isClusterEligible(cluster, spb.Spec.Placement) {
			clusters = append(clusters, cluster)
		}
	}

	return clusters, nil
}

// isClusterEligible checks if a cluster is eligible based on placement constraints
func (c *Controller) isClusterEligible(cluster *clusterv1alpha1.Cluster, placement *policyv1alpha1.Placement) bool {
	if placement == nil {
		return true
	}

	// Check cluster affinity
	if placement.ClusterAffinity != nil {
		if len(placement.ClusterAffinity.ClusterNames) > 0 {
			for _, name := range placement.ClusterAffinity.ClusterNames {
				if cluster.Name == name {
					return true
				}
			}
			return false
		}
	}

	// TODO: Add more placement constraint checks (labels, taints, etc.)
	return true
}

// getCurrentPodDistribution gets the current pod distribution across member clusters
func (c *Controller) getCurrentPodDistribution(ctx context.Context, spb *workv1alpha2.StatefulPodBinding, workload *appsv1.StatefulSet) (map[string][]int32, error) {
	distribution := make(map[string][]int32)

	// Get all member clusters
	clusterList := &clusterv1alpha1.ClusterList{}
	if err := c.Client.List(ctx, clusterList); err != nil {
		return nil, fmt.Errorf("failed to list clusters: %w", err)
	}

	// Check current distribution from existing clusters in StatefulPodBinding
	if len(spb.Spec.Clusters) > 0 {
		for _, cluster := range spb.Spec.Clusters {
			if len(cluster.PodIndices) > 0 {
				distribution[cluster.Name] = append([]int32{}, cluster.PodIndices...)
			}
		}
	}

	// TODO: Query actual pod distribution from member clusters
	// For now, we rely on the StatefulPodBinding spec as the source of truth

	return distribution, nil
}

// schedulePods schedules pods to clusters based on placement strategy
func (c *Controller) schedulePods(ctx context.Context, spb *workv1alpha2.StatefulPodBinding, workload *appsv1.StatefulSet, currentDistribution map[string][]int32, clusters []*clusterv1alpha1.Cluster) ([]workv1alpha2.TargetCluster, error) {
	replicas := spb.Spec.Replicas
	if replicas == 0 && workload.Spec.Replicas != nil {
		replicas = *workload.Spec.Replicas
	}

	// Initialize result with current distribution
	clusterPodMap := make(map[string][]int32)
	for clusterName, podIndices := range currentDistribution {
		clusterPodMap[clusterName] = append([]int32{}, podIndices...)
	}

	// Apply placement strategy to schedule pods
	if err := c.applyPlacementStrategy(spb.Spec.Placement, replicas, clusterPodMap, clusters); err != nil {
		return nil, fmt.Errorf("failed to apply placement strategy: %w", err)
	}

	// Convert to TargetCluster format (note: using TargetCluster instead of PodTargetCluster for now)
	var result []workv1alpha2.TargetCluster
	for clusterName, podIndices := range clusterPodMap {
		if len(podIndices) > 0 {
			sort.Slice(podIndices, func(i, j int) bool {
				return podIndices[i] < podIndices[j]
			})
			result = append(result, workv1alpha2.TargetCluster{
				Name:       clusterName,
				PodIndices: podIndices,
			})
		}
	}

	// Sort clusters by name for consistent output
	sort.Slice(result, func(i, j int) bool {
		return result[i].Name < result[j].Name
	})

	return result, nil
}

// applyPlacementStrategy applies the placement strategy to schedule pods
func (c *Controller) applyPlacementStrategy(placement *policyv1alpha1.Placement, replicas int32, clusterPodMap map[string][]int32, clusters []*clusterv1alpha1.Cluster) error {
	// Handle fixed pod placement first (highest priority)
	if placement.ReplicaScheduling != nil && len(placement.ReplicaScheduling.StatefulFixedPodPlacement) > 0 {
		for _, fixedPlacement := range placement.ReplicaScheduling.StatefulFixedPodPlacement {
			for _, podIndex := range fixedPlacement.PodIndexes {
				if podIndex >= replicas {
					continue // Skip invalid pod indices
				}

				// Remove pod from other clusters if it exists
				c.removePodFromOtherClusters(clusterPodMap, fixedPlacement.ClusterName, podIndex)

				// Add pod to the specified cluster
				if clusterPodMap[fixedPlacement.ClusterName] == nil {
					clusterPodMap[fixedPlacement.ClusterName] = []int32{}
				}
				if !c.containsPodIndex(clusterPodMap[fixedPlacement.ClusterName], podIndex) {
					clusterPodMap[fixedPlacement.ClusterName] = append(clusterPodMap[fixedPlacement.ClusterName], podIndex)
				}
			}
		}
	}

	// Handle weighted placement for remaining pods
	if placement.ReplicaScheduling != nil && placement.ReplicaScheduling.WeightPreference != nil {
		return c.applyWeightedPlacement(placement, replicas, clusterPodMap, clusters)
	}

	// Default: distribute remaining pods evenly
	return c.distributeRemainingPodsEvenly(replicas, clusterPodMap, clusters)
}

// removePodFromOtherClusters removes a pod index from all clusters except the specified one
func (c *Controller) removePodFromOtherClusters(clusterPodMap map[string][]int32, targetCluster string, podIndex int32) {
	for clusterName, podIndices := range clusterPodMap {
		if clusterName != targetCluster {
			for i, idx := range podIndices {
				if idx == podIndex {
					clusterPodMap[clusterName] = append(podIndices[:i], podIndices[i+1:]...)
					break
				}
			}
		}
	}
}

// containsPodIndex checks if a pod index exists in the slice
func (c *Controller) containsPodIndex(podIndices []int32, podIndex int32) bool {
	for _, idx := range podIndices {
		if idx == podIndex {
			return true
		}
	}
	return false
}

// applyWeightedPlacement applies weighted placement strategy
func (c *Controller) applyWeightedPlacement(placement *policyv1alpha1.Placement, replicas int32, clusterPodMap map[string][]int32, clusters []*clusterv1alpha1.Cluster) error {
	// Get assigned pods (from fixed placement)
	assignedPods := make(map[int32]bool)
	for _, podIndices := range clusterPodMap {
		for _, podIndex := range podIndices {
			assignedPods[podIndex] = true
		}
	}

	// Get unassigned pods
	var unassignedPods []int32
	for i := int32(0); i < replicas; i++ {
		if !assignedPods[i] {
			unassignedPods = append(unassignedPods, i)
		}
	}

	if len(unassignedPods) == 0 {
		return nil // All pods are already assigned
	}

	// Calculate cluster weights
	clusterWeights := c.calculateClusterWeights(placement.ReplicaScheduling.WeightPreference, clusters)

	// Distribute unassigned pods based on weights
	return c.distributePodsBasedOnWeights(unassignedPods, clusterPodMap, clusterWeights)
}

// calculateClusterWeights calculates weights for each cluster
func (c *Controller) calculateClusterWeights(weightPreference *policyv1alpha1.ClusterPreferences, clusters []*clusterv1alpha1.Cluster) map[string]int64 {
	weights := make(map[string]int64)

	if weightPreference.StaticWeightList != nil {
		for _, weight := range weightPreference.StaticWeightList {
			for _, cluster := range clusters {
				if c.clusterMatchesAffinity(cluster, &weight.TargetCluster) {
					weights[cluster.Name] = weight.Weight
				}
			}
		}
	}

	// Set default weight for clusters without explicit weight
	for _, cluster := range clusters {
		if _, exists := weights[cluster.Name]; !exists {
			weights[cluster.Name] = 1
		}
	}

	return weights
}

// clusterMatchesAffinity checks if a cluster matches the cluster affinity
func (c *Controller) clusterMatchesAffinity(cluster *clusterv1alpha1.Cluster, affinity *policyv1alpha1.ClusterAffinity) bool {
	if len(affinity.ClusterNames) > 0 {
		for _, name := range affinity.ClusterNames {
			if cluster.Name == name {
				return true
			}
		}
		return false
	}
	// TODO: Add label selector matching
	return true
}

// distributePodsBasedOnWeights distributes pods based on cluster weights
func (c *Controller) distributePodsBasedOnWeights(unassignedPods []int32, clusterPodMap map[string][]int32, clusterWeights map[string]int64) error {
	if len(unassignedPods) == 0 || len(clusterWeights) == 0 {
		return nil
	}

	// Calculate total weight
	var totalWeight int64
	var clusterNames []string
	for clusterName, weight := range clusterWeights {
		totalWeight += weight
		clusterNames = append(clusterNames, clusterName)
	}
	sort.Strings(clusterNames) // For consistent ordering

	// Distribute pods proportionally
	podIndex := 0
	for _, podIdx := range unassignedPods {
		// Use round-robin with weight consideration
		targetCluster := clusterNames[podIndex%len(clusterNames)]

		if clusterPodMap[targetCluster] == nil {
			clusterPodMap[targetCluster] = []int32{}
		}
		clusterPodMap[targetCluster] = append(clusterPodMap[targetCluster], podIdx)
		podIndex++
	}

	return nil
}

// distributeRemainingPodsEvenly distributes remaining pods evenly across clusters
func (c *Controller) distributeRemainingPodsEvenly(replicas int32, clusterPodMap map[string][]int32, clusters []*clusterv1alpha1.Cluster) error {
	if len(clusters) == 0 {
		return fmt.Errorf("no clusters available for scheduling")
	}

	// Get assigned pods
	assignedPods := make(map[int32]bool)
	for _, podIndices := range clusterPodMap {
		for _, podIndex := range podIndices {
			assignedPods[podIndex] = true
		}
	}

	// Get unassigned pods
	var unassignedPods []int32
	for i := int32(0); i < replicas; i++ {
		if !assignedPods[i] {
			unassignedPods = append(unassignedPods, i)
		}
	}

	if len(unassignedPods) == 0 {
		return nil // All pods are already assigned
	}

	// Distribute unassigned pods evenly
	clusterIndex := 0
	for _, podIdx := range unassignedPods {
		clusterName := clusters[clusterIndex%len(clusters)].Name

		if clusterPodMap[clusterName] == nil {
			clusterPodMap[clusterName] = []int32{}
		}
		clusterPodMap[clusterName] = append(clusterPodMap[clusterName], podIdx)
		clusterIndex++
	}

	return nil
}

// updateStatefulPodBinding updates the StatefulPodBinding with scheduled clusters
func (c *Controller) updateStatefulPodBinding(ctx context.Context, spb *workv1alpha2.StatefulPodBinding, scheduledClusters []workv1alpha2.TargetCluster) (controllerruntime.Result, error) {
	// Check if update is needed
	if c.clustersEqual(spb.Spec.Clusters, scheduledClusters) {
		klog.V(4).Infof("StatefulPodBinding %s/%s clusters unchanged, skipping update", spb.Namespace, spb.Name)
		return controllerruntime.Result{}, nil
	}

	// Update the spec
	spb.Spec.Clusters = scheduledClusters

	if err := c.Client.Update(ctx, spb); err != nil {
		klog.Errorf("Failed to update StatefulPodBinding %s/%s: %v", spb.Namespace, spb.Name, err)
		return controllerruntime.Result{Requeue: true}, err
	}

	klog.Infof("Successfully updated StatefulPodBinding %s/%s with %d clusters", spb.Namespace, spb.Name, len(scheduledClusters))
	return controllerruntime.Result{}, nil
}

// clustersEqual checks if two cluster lists are equal
func (c *Controller) clustersEqual(current, new []workv1alpha2.TargetCluster) bool {
	if len(current) != len(new) {
		return false
	}

	// Create maps for comparison
	currentMap := make(map[string][]int32)
	for _, cluster := range current {
		currentMap[cluster.Name] = cluster.PodIndices
	}

	newMap := make(map[string][]int32)
	for _, cluster := range new {
		newMap[cluster.Name] = cluster.PodIndices
	}

	// Compare maps
	for clusterName, currentIndices := range currentMap {
		newIndices, exists := newMap[clusterName]
		if !exists || !c.slicesEqual(currentIndices, newIndices) {
			return false
		}
	}

	for clusterName := range newMap {
		if _, exists := currentMap[clusterName]; !exists {
			return false
		}
	}

	return true
}

// slicesEqual checks if two int32 slices are equal
func (c *Controller) slicesEqual(a, b []int32) bool {
	if len(a) != len(b) {
		return false
	}

	// Sort both slices for comparison
	aCopy := make([]int32, len(a))
	bCopy := make([]int32, len(b))
	copy(aCopy, a)
	copy(bCopy, b)

	sort.Slice(aCopy, func(i, j int) bool { return aCopy[i] < aCopy[j] })
	sort.Slice(bCopy, func(i, j int) bool { return bCopy[i] < bCopy[j] })

	for i := range aCopy {
		if aCopy[i] != bCopy[i] {
			return false
		}
	}

	return true
}

// SetupWithManager sets up the controller with the Manager.
func (c *Controller) SetupWithManager(mgr controllerruntime.Manager) error {
	statefulSetMapFunc := handler.MapFunc(
		func(ctx context.Context, obj client.Object) []reconcile.Request {
			return c.findStatefulPodBindingsForStatefulSet(obj)
		})

	return controllerruntime.NewControllerManagedBy(mgr).
		For(&workv1alpha2.StatefulPodBinding{}).
		Watches(&appsv1.StatefulSet{}, handler.EnqueueRequestsFromMapFunc(statefulSetMapFunc)).
		WithOptions(controller.Options{
			RateLimiter: ratelimiterflag.DefaultControllerRateLimiter(c.RateLimiterOptions),
		}).
		Complete(c)
}

// findStatefulPodBindingsForStatefulSet finds StatefulPodBindings that reference a StatefulSet
func (c *Controller) findStatefulPodBindingsForStatefulSet(obj client.Object) []reconcile.Request {
	statefulSet, ok := obj.(*appsv1.StatefulSet)
	if !ok {
		klog.Errorf("Expected StatefulSet but got %T", obj)
		return nil
	}

	spbList := &workv1alpha2.StatefulPodBindingList{}
	if err := c.Client.List(context.TODO(), spbList, client.InNamespace(statefulSet.Namespace)); err != nil {
		klog.Errorf("Failed to list StatefulPodBindings: %v", err)
		return nil
	}

	var requests []reconcile.Request
	for _, spb := range spbList.Items {
		if spb.Spec.Resource.Name == statefulSet.Name &&
			spb.Spec.Resource.Namespace == statefulSet.Namespace &&
			spb.Spec.Resource.Kind == "StatefulSet" {
			requests = append(requests, reconcile.Request{
				NamespacedName: types.NamespacedName{
					Namespace: spb.Namespace,
					Name:      spb.Name,
				},
			})
		}
	}

	return requests
}
