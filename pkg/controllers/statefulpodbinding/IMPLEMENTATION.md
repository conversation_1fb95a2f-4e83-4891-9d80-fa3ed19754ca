# StatefulPodBinding Controller Implementation

## Overview

This document describes the implementation of the StatefulPodBinding controller, which handles the scheduling of stateful workload pods across different member clusters in Karmada. The controller now supports multiple types of stateful workloads through a workload abstraction layer.

## Features Implemented

### Function 1: StatefulPodBinding Reconciliation and Pod Scheduling

The controller implements the core functionality described in the readme.md file:

1. **Pod-level Distribution**: Distributes stateful workload pods across member clusters based on placement strategies
2. **Placement Strategy Support**: 
   - `fixedPodPlacement`: Highest priority for specific pod indices
   - `weightedPlacement`: Weight-based distribution for remaining pods
3. **Minimal Pod Impact**: Attempts to minimize the number of affected pods during rescheduling

## Architecture

### Controller Structure

```go
type Controller struct {
    client.Client
    Scheme                      *runtime.Scheme
    EventRecorder               record.EventRecorder
    RateLimiterOptions          ratelimiterflag.Options
    ClusterClientSetFunc        func(clusterName string, client client.Client) (*util.ClusterClient, error)
    ClusterDynamicClientSetFunc func(clusterName string, client client.Client) (*util.DynamicClusterClient, error)
    Algorithm                   core.ScheduleAlgorithm
    schedulerCache              schedulercache.Cache

    // Workload abstraction components
    ResourceInterpreter         resourceinterpreter.ResourceInterpreter
    WorkloadFactory            *WorkloadFactory
}
```

### Workload Abstraction

The controller now supports multiple types of stateful workloads through a workload abstraction layer:

```go
type StatefulWorkload interface {
    GetReplicas() int32
    GetGVK() schema.GroupVersionKind
    GetNamespacedName() types.NamespacedName
    GetObject() *unstructured.Unstructured
}

type WorkloadFactory struct {
    client              client.Client
    resourceInterpreter resourceinterpreter.ResourceInterpreter
}
```

**Supported Workload Types:**
- Kubernetes StatefulSet (`apps/v1`)
- Kruise StatefulSet, CloneSet, AdvancedStatefulSet
- StrimziPodSet (`core.strimzi.io/v1beta1`, `core.strimzi.io/v1beta2`)
- PostgreSQL, MySQL, Redis clusters
- Custom stateful workloads

### Key Methods

1. **Reconcile**: Main reconciliation loop that handles StatefulPodBinding events
2. **getWorkload**: Retrieves the referenced stateful workload (replaces getStatefulSet)
3. **getAvailableClusters**: Gets list of available and ready member clusters
4. **getCurrentPodDistribution**: Analyzes current pod distribution across clusters
5. **schedulePods**: Core scheduling logic that applies placement strategies
6. **applyPlacementStrategy**: Applies fixed and weighted placement strategies
7. **updateStatefulPodBinding**: Updates the StatefulPodBinding with scheduling results

### Workload Factory Methods

1. **GetWorkload**: Creates a StatefulWorkload abstraction from StatefulPodBinding reference
2. **createWorkload**: Internal method to wrap unstructured objects
3. **extractReplicas**: Uses ResourceInterpreter or fallback extraction for replica count

## Placement Strategy Implementation

### Fixed Pod Placement (Highest Priority)

```go
// Example configuration
StatefulFixedPodPlacement: []policyv1alpha1.StatefulFixedPodPlacement{
    {
        ClusterName: "cluster1",
        PodIndexes:  []int32{0, 2}, // Pods 0 and 2 go to cluster1
    },
    {
        ClusterName: "cluster2", 
        PodIndexes:  []int32{1},    // Pod 1 goes to cluster2
    },
}
```

### Weighted Placement

```go
// Example configuration
WeightPreference: &policyv1alpha1.ClusterPreferences{
    StaticWeightList: []policyv1alpha1.StaticClusterWeight{
        {
            TargetCluster: policyv1alpha1.ClusterAffinity{
                ClusterNames: []string{"cluster1"},
            },
            Weight: 2, // cluster1 gets 2x weight
        },
        {
            TargetCluster: policyv1alpha1.ClusterAffinity{
                ClusterNames: []string{"cluster2"},
            },
            Weight: 1, // cluster2 gets 1x weight
        },
    },
}
```

## Scheduling Algorithm

1. **Fixed Placement First**: Process `StatefulFixedPodPlacement` configurations
   - Remove pods from other clusters if they exist elsewhere
   - Assign pods to specified clusters
   
2. **Weighted Distribution**: For remaining unassigned pods
   - Calculate cluster weights from `WeightPreference`
   - Distribute pods proportionally based on weights
   
3. **Even Distribution**: Fallback for clusters without explicit weights
   - Distribute remaining pods evenly across available clusters

## Output Format

The controller writes results to `StatefulPodBinding.Spec.Clusters`:

```go
type TargetCluster struct {
    Name       string  `json:"name"`
    PodIndices []int32 `json:"podIndices"`
}
```

Example output:
```yaml
spec:
  clusters:
  - name: cluster1
    podIndices: [0, 2]
  - name: cluster2  
    podIndices: [1]
```

## Controller Setup

The controller watches:
- **StatefulPodBinding**: Primary resource for reconciliation
- **StatefulSet**: Triggers reconciliation when referenced StatefulSet changes

## Testing

The implementation includes comprehensive tests:
- `TestController_Reconcile`: Tests full reconciliation flow
- `TestController_applyPlacementStrategy`: Tests placement strategy logic

## Integration Points

### Scheduler Integration
- Uses `core.ScheduleAlgorithm` interface for advanced scheduling
- Integrates with `schedulercache.Cache` for cluster state

### Event Handling
- Records events for scheduling decisions
- Provides detailed logging for troubleshooting

## Error Handling

- Graceful handling of missing StatefulSets
- Validation of cluster availability
- Proper error reporting through events and logs

## Future Enhancements

1. **Dynamic Pod Querying**: Currently relies on StatefulPodBinding spec; could be enhanced to query actual pod distribution
2. **Advanced Affinity**: Support for label selectors in cluster affinity
3. **Health-based Scheduling**: Consider cluster health in scheduling decisions
4. **Pod Migration**: Support for controlled pod migration between clusters

## Usage Example

```yaml
apiVersion: work.karmada.io/v1alpha2
kind: StatefulPodBinding
metadata:
  name: my-statefulset-binding
  namespace: default
spec:
  resource:
    apiVersion: apps/v1
    kind: StatefulSet
    name: my-statefulset
    namespace: default
  replicas: 5
  placement:
    replicaScheduling:
      statefulFixedPodPlacement:
      - clusterName: cluster1
        podIndexes: [0, 1]  # Master pods
      - clusterName: cluster2
        podIndexes: [4]     # Special pod
      weightPreference:
        staticWeightList:
        - targetCluster:
            clusterNames: ["cluster1"]
          weight: 2
        - targetCluster:
            clusterNames: ["cluster2"] 
          weight: 1
```

This would result in:
- Pods 0, 1 → cluster1 (fixed)
- Pod 4 → cluster2 (fixed)  
- Pods 2, 3 → distributed by weight (likely both to cluster1 due to 2:1 ratio)
