package statefulpodbinding

import (
	"context"
	"testing"

	appsv1 "k8s.io/api/apps/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/tools/record"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"

	clusterv1alpha1 "github.com/karmada-io/karmada/pkg/apis/cluster/v1alpha1"
	configv1alpha1 "github.com/karmada-io/karmada/pkg/apis/config/v1alpha1"
	policyv1alpha1 "github.com/karmada-io/karmada/pkg/apis/policy/v1alpha1"
	workv1alpha2 "github.com/karmada-io/karmada/pkg/apis/work/v1alpha2"
	"github.com/karmada-io/karmada/pkg/sharedcli/ratelimiterflag"
	"github.com/karmada-io/karmada/pkg/util/gclient"
)

// mockResourceInterpreter is a mock implementation of ResourceInterpreter for testing
type mockResourceInterpreter struct{}

func (m *mockResourceInterpreter) Start(ctx context.Context) error { return nil }
func (m *mockResourceInterpreter) HookEnabled(objGVK schema.GroupVersionKind, operationType configv1alpha1.InterpreterOperation) bool {
	return false
}
func (m *mockResourceInterpreter) GetReplicas(object *unstructured.Unstructured) (replica int32, replicaRequires *workv1alpha2.ReplicaRequirements, err error) {
	// Extract replicas from StatefulSet
	if object.GetKind() == "StatefulSet" {
		if replicas, found, err := unstructured.NestedInt64(object.Object, "spec", "replicas"); err == nil && found {
			return int32(replicas), &workv1alpha2.ReplicaRequirements{}, nil
		}
	}
	return 1, &workv1alpha2.ReplicaRequirements{}, nil
}
func (m *mockResourceInterpreter) ReviseReplica(object *unstructured.Unstructured, replica int64) (*unstructured.Unstructured, error) {
	return object, nil
}
func (m *mockResourceInterpreter) Retain(desired *unstructured.Unstructured, observed *unstructured.Unstructured) (*unstructured.Unstructured, error) {
	return desired, nil
}
func (m *mockResourceInterpreter) AggregateStatus(object *unstructured.Unstructured, aggregatedStatusItems []workv1alpha2.AggregatedStatusItem) (*unstructured.Unstructured, error) {
	return object, nil
}
func (m *mockResourceInterpreter) GetDependencies(object *unstructured.Unstructured) (dependencies []configv1alpha1.DependentObjectReference, err error) {
	return nil, nil
}
func (m *mockResourceInterpreter) ReflectStatus(object *unstructured.Unstructured) (status *runtime.RawExtension, err error) {
	return nil, nil
}
func (m *mockResourceInterpreter) InterpretHealth(object *unstructured.Unstructured) (healthy bool, err error) {
	return true, nil
}
func (m *mockResourceInterpreter) GetPartition(object *unstructured.Unstructured) (partition int32, strategy *workv1alpha2.UpdateStrategy, err error) {
	return 0, nil, nil
}
func (m *mockResourceInterpreter) RevisePartition(object *unstructured.Unstructured, partition int32) (*unstructured.Unstructured, error) {
	return object, nil
}

func TestController_Reconcile(t *testing.T) {
	scheme := gclient.NewSchema()

	// Create test objects
	statefulSet := &appsv1.StatefulSet{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-statefulset",
			Namespace: "default",
		},
		Spec: appsv1.StatefulSetSpec{
			Replicas: int32Ptr(3),
		},
	}

	spb := &workv1alpha2.StatefulPodBinding{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-spb",
			Namespace: "default",
		},
		Spec: workv1alpha2.ResourceBindingSpec{
			Resource: workv1alpha2.ObjectReference{
				APIVersion: "apps/v1",
				Kind:       "StatefulSet",
				Name:       "test-statefulset",
				Namespace:  "default",
			},
			Replicas: 3,
			Placement: &policyv1alpha1.Placement{
				ReplicaScheduling: &policyv1alpha1.ReplicaSchedulingStrategy{
					WeightPreference: &policyv1alpha1.ClusterPreferences{
						StaticWeightList: []policyv1alpha1.StaticClusterWeight{
							{
								TargetCluster: policyv1alpha1.ClusterAffinity{
									ClusterNames: []string{"cluster1"},
								},
								Weight: 2,
							},
							{
								TargetCluster: policyv1alpha1.ClusterAffinity{
									ClusterNames: []string{"cluster2"},
								},
								Weight: 1,
							},
						},
					},
				},
			},
		},
	}

	cluster1 := &clusterv1alpha1.Cluster{
		ObjectMeta: metav1.ObjectMeta{
			Name: "cluster1",
		},
		Status: clusterv1alpha1.ClusterStatus{
			Conditions: []metav1.Condition{
				{
					Type:   clusterv1alpha1.ClusterConditionReady,
					Status: metav1.ConditionTrue,
				},
			},
		},
	}

	cluster2 := &clusterv1alpha1.Cluster{
		ObjectMeta: metav1.ObjectMeta{
			Name: "cluster2",
		},
		Status: clusterv1alpha1.ClusterStatus{
			Conditions: []metav1.Condition{
				{
					Type:   clusterv1alpha1.ClusterConditionReady,
					Status: metav1.ConditionTrue,
				},
			},
		},
	}

	// Create fake client
	fakeClient := fake.NewClientBuilder().
		WithScheme(scheme).
		WithObjects(statefulSet, spb, cluster1, cluster2).
		Build()

	// Create mock resource interpreter
	resourceInterpreter := &mockResourceInterpreter{}

	// Create controller
	controller := &Controller{
		Client:        fakeClient,
		Scheme:        scheme,
		EventRecorder: &record.FakeRecorder{},
		RateLimiterOptions: ratelimiterflag.Options{
			RateLimiterQPS:        10,
			RateLimiterBucketSize: 100,
		},
		ResourceInterpreter: resourceInterpreter,
		WorkloadFactory:     NewWorkloadFactory(fakeClient, resourceInterpreter),
	}

	// Test reconcile
	req := reconcile.Request{
		NamespacedName: types.NamespacedName{
			Namespace: "default",
			Name:      "test-spb",
		},
	}

	result, err := controller.Reconcile(context.TODO(), req)
	if err != nil {
		t.Fatalf("Reconcile failed: %v", err)
	}

	if result.Requeue {
		t.Errorf("Expected no requeue, but got requeue")
	}

	// Verify the StatefulPodBinding was updated with clusters
	updatedSPB := &workv1alpha2.StatefulPodBinding{}
	err = fakeClient.Get(context.TODO(), types.NamespacedName{
		Namespace: "default",
		Name:      "test-spb",
	}, updatedSPB)
	if err != nil {
		t.Fatalf("Failed to get updated StatefulPodBinding: %v", err)
	}

	if len(updatedSPB.Spec.Clusters) == 0 {
		t.Errorf("Expected clusters to be scheduled, but got empty clusters")
	}

	// Verify all pods are scheduled
	totalPods := int32(0)
	for _, cluster := range updatedSPB.Spec.Clusters {
		totalPods += int32(len(cluster.PodIndices))
	}

	if totalPods != 3 {
		t.Errorf("Expected 3 pods to be scheduled, but got %d", totalPods)
	}
}

func TestController_applyPlacementStrategy(t *testing.T) {
	controller := &Controller{}

	// Test fixed pod placement
	placement := &policyv1alpha1.Placement{
		ReplicaScheduling: &policyv1alpha1.ReplicaSchedulingStrategy{
			StatefulFixedPodPlacement: []policyv1alpha1.StatefulFixedPodPlacement{
				{
					ClusterName: "cluster1",
					PodIndexes:  []int32{0, 2},
				},
				{
					ClusterName: "cluster2",
					PodIndexes:  []int32{1},
				},
			},
		},
	}

	clusterPodMap := make(map[string][]int32)
	clusters := []*clusterv1alpha1.Cluster{
		{ObjectMeta: metav1.ObjectMeta{Name: "cluster1"}},
		{ObjectMeta: metav1.ObjectMeta{Name: "cluster2"}},
	}

	err := controller.applyPlacementStrategy(placement, 3, clusterPodMap, clusters)
	if err != nil {
		t.Fatalf("applyPlacementStrategy failed: %v", err)
	}

	// Verify fixed placement
	if len(clusterPodMap["cluster1"]) != 2 {
		t.Errorf("Expected cluster1 to have 2 pods, got %d", len(clusterPodMap["cluster1"]))
	}
	if len(clusterPodMap["cluster2"]) != 1 {
		t.Errorf("Expected cluster2 to have 1 pod, got %d", len(clusterPodMap["cluster2"]))
	}

	// Check specific pod indices
	cluster1Pods := clusterPodMap["cluster1"]
	if !contains(cluster1Pods, 0) || !contains(cluster1Pods, 2) {
		t.Errorf("cluster1 should contain pods 0 and 2, got %v", cluster1Pods)
	}

	cluster2Pods := clusterPodMap["cluster2"]
	if !contains(cluster2Pods, 1) {
		t.Errorf("cluster2 should contain pod 1, got %v", cluster2Pods)
	}
}

func contains(slice []int32, item int32) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

func int32Ptr(i int32) *int32 {
	return &i
}

func TestController_schedulePodsIndices(t *testing.T) {
	tests := []struct {
		name               string
		spb                *workv1alpha2.StatefulPodBinding
		currentPodsMember  []string
		targets            []workv1alpha2.TargetCluster
		expectedResult     []workv1alpha2.TargetCluster
		expectError        bool
	}{
		{
			name: "basic pod distribution without fixed placement",
			spb: &workv1alpha2.StatefulPodBinding{
				ObjectMeta: metav1.ObjectMeta{
					Name:      "test-spb",
					Namespace: "default",
				},
				Spec: workv1alpha2.StatefulPodBindingSpec{
					Replicas: 5,
				},
			},
			currentPodsMember: []string{"", "", "", "", ""},
			targets: []workv1alpha2.TargetCluster{
				{Name: "cluster1", Replicas: 3},
				{Name: "cluster2", Replicas: 2},
			},
			expectedResult: []workv1alpha2.TargetCluster{
				{Name: "cluster1", Replicas: 3, PodIndices: []int32{0, 1, 2}},
				{Name: "cluster2", Replicas: 2, PodIndices: []int32{3, 4}},
			},
			expectError: false,
		},
		{
			name: "with fixed pod placement",
			spb: &workv1alpha2.StatefulPodBinding{
				ObjectMeta: metav1.ObjectMeta{
					Name:      "test-spb-fixed",
					Namespace: "default",
				},
				Spec: workv1alpha2.StatefulPodBindingSpec{
					Replicas: 6,
					Placement: &policyv1alpha1.Placement{
						ReplicaScheduling: &policyv1alpha1.ReplicaSchedulingStrategy{
							StatefulFixedPodPlacement: []workv1alpha2.StatefulFixedPodPlacement{
								{
									ClusterName: "cluster1",
									PodIndexes:  []int32{0, 2, 4},
								},
								{
									ClusterName: "cluster2",
									PodIndexes:  []int32{1},
								},
							},
						},
					},
				},
			},
			currentPodsMember: []string{"", "", "", "", "", ""},
			targets: []workv1alpha2.TargetCluster{
				{Name: "cluster1", Replicas: 4},
				{Name: "cluster2", Replicas: 2},
			},
			expectedResult: []workv1alpha2.TargetCluster{
				{Name: "cluster1", Replicas: 4, PodIndices: []int32{0, 2, 3, 4}},
				{Name: "cluster2", Replicas: 2, PodIndices: []int32{1, 5}},
			},
			expectError: false,
		},
		{
			name: "preserve existing pod distribution",
			spb: &workv1alpha2.StatefulPodBinding{
				ObjectMeta: metav1.ObjectMeta{
					Name:      "test-spb-preserve",
					Namespace: "default",
				},
				Spec: workv1alpha2.StatefulPodBindingSpec{
					Replicas: 4,
				},
			},
			currentPodsMember: []string{"cluster1", "cluster2", "cluster1", "cluster2"},
			targets: []workv1alpha2.TargetCluster{
				{Name: "cluster1", Replicas: 2},
				{Name: "cluster2", Replicas: 2},
			},
			expectedResult: []workv1alpha2.TargetCluster{
				{Name: "cluster1", Replicas: 2, PodIndices: []int32{0, 2}},
				{Name: "cluster2", Replicas: 2, PodIndices: []int32{1, 3}},
			},
			expectError: false,
		},
		{
			name: "zero replicas",
			spb: &workv1alpha2.StatefulPodBinding{
				ObjectMeta: metav1.ObjectMeta{
					Name:      "test-spb-zero",
					Namespace: "default",
				},
				Spec: workv1alpha2.StatefulPodBindingSpec{
					Replicas: 0,
				},
			},
			currentPodsMember: []string{},
			targets: []workv1alpha2.TargetCluster{
				{Name: "cluster1", Replicas: 0},
			},
			expectedResult: []workv1alpha2.TargetCluster{
				{Name: "cluster1", Replicas: 0},
			},
			expectError: false,
		},
		{
			name: "invalid fixed placement - duplicate indices",
			spb: &workv1alpha2.StatefulPodBinding{
				ObjectMeta: metav1.ObjectMeta{
					Name:      "test-spb-invalid",
					Namespace: "default",
				},
				Spec: workv1alpha2.StatefulPodBindingSpec{
					Replicas: 3,
					Placement: &policyv1alpha1.Placement{
						ReplicaScheduling: &policyv1alpha1.ReplicaSchedulingStrategy{
							StatefulFixedPodPlacement: []workv1alpha2.StatefulFixedPodPlacement{
								{
									ClusterName: "cluster1",
									PodIndexes:  []int32{0, 1},
								},
								{
									ClusterName: "cluster2",
									PodIndexes:  []int32{1, 2}, // duplicate index 1
								},
							},
						},
					},
				},
			},
			currentPodsMember: []string{"", "", ""},
			targets: []workv1alpha2.TargetCluster{
				{Name: "cluster1", Replicas: 2},
				{Name: "cluster2", Replicas: 1},
			},
			expectedResult: []workv1alpha2.TargetCluster{
				{Name: "cluster1", Replicas: 2, PodIndices: []int32{0, 1}},
				{Name: "cluster2", Replicas: 1, PodIndices: []int32{2}},
			},
			expectError: false, // Should handle gracefully by ignoring duplicate
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create controller with mock dependencies
			scheme := runtime.NewScheme()
			_ = workv1alpha2.AddToScheme(scheme)
			_ = clusterv1alpha1.AddToScheme(scheme)
			_ = appsv1.AddToScheme(scheme)

			fakeClient := fake.NewClientBuilder().WithScheme(scheme).Build()
			controller := &Controller{
				Client:              fakeClient,
				EventRecorder:       &record.FakeRecorder{},
				RESTMapper:          nil,
				ResourceInterpreter: &mockResourceInterpreter{},
				WorkloadFactory:     NewWorkloadFactory(&mockResourceInterpreter{}),
				RateLimiterOptions:  ratelimiterflag.Options{},
			}

			// Call the function under test
			result, err := controller.schedulePodsIndices(context.TODO(), tt.spb, tt.currentPodsMember, tt.targets)

			// Check error expectation
			if tt.expectError && err == nil {
				t.Errorf("Expected error but got none")
				return
			}
			if !tt.expectError && err != nil {
				t.Errorf("Unexpected error: %v", err)
				return
			}

			// Skip result validation if error was expected
			if tt.expectError {
				return
			}

			// Validate result
			if len(result) != len(tt.expectedResult) {
				t.Errorf("Expected %d clusters, got %d", len(tt.expectedResult), len(result))
				return
			}

			// Create maps for easier comparison
			resultMap := make(map[string]workv1alpha2.TargetCluster)
			expectedMap := make(map[string]workv1alpha2.TargetCluster)

			for _, cluster := range result {
				resultMap[cluster.Name] = cluster
			}
			for _, cluster := range tt.expectedResult {
				expectedMap[cluster.Name] = cluster
			}

			// Compare each cluster
			for clusterName, expected := range expectedMap {
				actual, exists := resultMap[clusterName]
				if !exists {
					t.Errorf("Expected cluster %s not found in result", clusterName)
					continue
				}

				if actual.Replicas != expected.Replicas {
					t.Errorf("Cluster %s: expected %d replicas, got %d", clusterName, expected.Replicas, actual.Replicas)
				}

				if len(actual.PodIndices) != len(expected.PodIndices) {
					t.Errorf("Cluster %s: expected %d pod indices, got %d", clusterName, len(expected.PodIndices), len(actual.PodIndices))
					continue
				}

				// Check pod indices (they should be sorted)
				for i, expectedIndex := range expected.PodIndices {
					if actual.PodIndices[i] != expectedIndex {
						t.Errorf("Cluster %s: expected pod index %d at position %d, got %d", clusterName, expectedIndex, i, actual.PodIndices[i])
					}
				}
			}

			// Validate that all pod indices are assigned exactly once
			allAssignedIndices := make(map[int32]string)
			for _, cluster := range result {
				for _, podIndex := range cluster.PodIndices {
					if existingCluster, exists := allAssignedIndices[podIndex]; exists {
						t.Errorf("Pod index %d assigned to both cluster %s and %s", podIndex, existingCluster, cluster.Name)
					}
					allAssignedIndices[podIndex] = cluster.Name
				}
			}

			// Check that all expected indices are assigned
			for i := int32(0); i < tt.spb.Spec.Replicas; i++ {
				if _, assigned := allAssignedIndices[i]; !assigned {
					t.Errorf("Pod index %d was not assigned to any cluster", i)
				}
			}
		})
	}
}
