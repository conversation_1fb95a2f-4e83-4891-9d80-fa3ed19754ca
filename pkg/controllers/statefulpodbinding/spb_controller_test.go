package statefulpodbinding

import (
	"context"
	"testing"

	appsv1 "k8s.io/api/apps/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/tools/record"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"
	"sigs.k8s.io/controller-runtime/pkg/reconcile"

	clusterv1alpha1 "github.com/karmada-io/karmada/pkg/apis/cluster/v1alpha1"
	policyv1alpha1 "github.com/karmada-io/karmada/pkg/apis/policy/v1alpha1"
	workv1alpha2 "github.com/karmada-io/karmada/pkg/apis/work/v1alpha2"
	"github.com/karmada-io/karmada/pkg/sharedcli/ratelimiterflag"
	"github.com/karmada-io/karmada/pkg/util/gclient"
)

func TestController_Reconcile(t *testing.T) {
	scheme := gclient.NewSchema()
	
	// Create test objects
	statefulSet := &appsv1.StatefulSet{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-statefulset",
			Namespace: "default",
		},
		Spec: appsv1.StatefulSetSpec{
			Replicas: int32Ptr(3),
		},
	}

	spb := &workv1alpha2.StatefulPodBinding{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-spb",
			Namespace: "default",
		},
		Spec: workv1alpha2.StatefulPodBindingSpec{
			Resource: workv1alpha2.ObjectReference{
				APIVersion: "apps/v1",
				Kind:       "StatefulSet",
				Name:       "test-statefulset",
				Namespace:  "default",
			},
			Replicas: 3,
			Placement: &policyv1alpha1.Placement{
				ReplicaScheduling: &policyv1alpha1.ReplicaSchedulingStrategy{
					WeightPreference: &policyv1alpha1.ClusterPreferences{
						StaticWeightList: []policyv1alpha1.StaticClusterWeight{
							{
								TargetCluster: policyv1alpha1.ClusterAffinity{
									ClusterNames: []string{"cluster1"},
								},
								Weight: 2,
							},
							{
								TargetCluster: policyv1alpha1.ClusterAffinity{
									ClusterNames: []string{"cluster2"},
								},
								Weight: 1,
							},
						},
					},
				},
			},
		},
	}

	cluster1 := &clusterv1alpha1.Cluster{
		ObjectMeta: metav1.ObjectMeta{
			Name: "cluster1",
		},
		Status: clusterv1alpha1.ClusterStatus{
			Conditions: []metav1.Condition{
				{
					Type:   clusterv1alpha1.ClusterConditionReady,
					Status: metav1.ConditionTrue,
				},
			},
		},
	}

	cluster2 := &clusterv1alpha1.Cluster{
		ObjectMeta: metav1.ObjectMeta{
			Name: "cluster2",
		},
		Status: clusterv1alpha1.ClusterStatus{
			Conditions: []metav1.Condition{
				{
					Type:   clusterv1alpha1.ClusterConditionReady,
					Status: metav1.ConditionTrue,
				},
			},
		},
	}

	// Create fake client
	fakeClient := fake.NewClientBuilder().
		WithScheme(scheme).
		WithObjects(statefulSet, spb, cluster1, cluster2).
		Build()

	// Create controller
	controller := &Controller{
		Client:        fakeClient,
		Scheme:        scheme,
		EventRecorder: &record.FakeRecorder{},
		RateLimiterOptions: ratelimiterflag.Options{
			RateLimiterQPS:        10,
			RateLimiterBucketSize: 100,
		},
	}

	// Test reconcile
	req := reconcile.Request{
		NamespacedName: types.NamespacedName{
			Namespace: "default",
			Name:      "test-spb",
		},
	}

	result, err := controller.Reconcile(context.TODO(), req)
	if err != nil {
		t.Fatalf("Reconcile failed: %v", err)
	}

	if result.Requeue {
		t.Errorf("Expected no requeue, but got requeue")
	}

	// Verify the StatefulPodBinding was updated with clusters
	updatedSPB := &workv1alpha2.StatefulPodBinding{}
	err = fakeClient.Get(context.TODO(), types.NamespacedName{
		Namespace: "default",
		Name:      "test-spb",
	}, updatedSPB)
	if err != nil {
		t.Fatalf("Failed to get updated StatefulPodBinding: %v", err)
	}

	if len(updatedSPB.Spec.Clusters) == 0 {
		t.Errorf("Expected clusters to be scheduled, but got empty clusters")
	}

	// Verify all pods are scheduled
	totalPods := int32(0)
	for _, cluster := range updatedSPB.Spec.Clusters {
		totalPods += int32(len(cluster.PodIndices))
	}

	if totalPods != 3 {
		t.Errorf("Expected 3 pods to be scheduled, but got %d", totalPods)
	}
}

func TestController_applyPlacementStrategy(t *testing.T) {
	controller := &Controller{}

	// Test fixed pod placement
	placement := &policyv1alpha1.Placement{
		ReplicaScheduling: &policyv1alpha1.ReplicaSchedulingStrategy{
			StatefulFixedPodPlacement: []policyv1alpha1.StatefulFixedPodPlacement{
				{
					ClusterName: "cluster1",
					PodIndexes:  []int32{0, 2},
				},
				{
					ClusterName: "cluster2",
					PodIndexes:  []int32{1},
				},
			},
		},
	}

	clusterPodMap := make(map[string][]int32)
	clusters := []*clusterv1alpha1.Cluster{
		{ObjectMeta: metav1.ObjectMeta{Name: "cluster1"}},
		{ObjectMeta: metav1.ObjectMeta{Name: "cluster2"}},
	}

	err := controller.applyPlacementStrategy(placement, 3, clusterPodMap, clusters)
	if err != nil {
		t.Fatalf("applyPlacementStrategy failed: %v", err)
	}

	// Verify fixed placement
	if len(clusterPodMap["cluster1"]) != 2 {
		t.Errorf("Expected cluster1 to have 2 pods, got %d", len(clusterPodMap["cluster1"]))
	}
	if len(clusterPodMap["cluster2"]) != 1 {
		t.Errorf("Expected cluster2 to have 1 pod, got %d", len(clusterPodMap["cluster2"]))
	}

	// Check specific pod indices
	cluster1Pods := clusterPodMap["cluster1"]
	if !contains(cluster1Pods, 0) || !contains(cluster1Pods, 2) {
		t.Errorf("cluster1 should contain pods 0 and 2, got %v", cluster1Pods)
	}

	cluster2Pods := clusterPodMap["cluster2"]
	if !contains(cluster2Pods, 1) {
		t.Errorf("cluster2 should contain pod 1, got %v", cluster2Pods)
	}
}

func contains(slice []int32, item int32) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

func int32Ptr(i int32) *int32 {
	return &i
}
