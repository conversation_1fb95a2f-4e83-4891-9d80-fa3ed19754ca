# Karmada Roadmap

This document defines a high level roadmap for Karmada development and upcoming releases.
Community and contributor involvement is vital for successfully implementing all desired items for each release.
We hope that the items listed below will inspire further engagement from the community to keep Karmada progressing and shipping exciting and valuable features.

## 2023 H1
- Cluster scheduling group
- Multiple schedulers support
- Application-level failover
- Multi-cluster HPA (Horizontal Pod Autoscaling)
- Karmada operator
- ResourceInterpreter include third-party CRD resources
- Karmada playground

## 2023 H2
- Karmada Dashboard - alpha release
- Multi-cluster workflow
- Cluster addon management
- Multi-cluster Application
- Multi-cluster monitoring
- Multi-cluster logging
- Multi-cluster storage
- Multi-cluster RBAC
- Multi-cluster networking
- Data migration across clusters
- Integration with terraform
- Cluster lifecycle management
- Image registry across clouds
- Multi-cluster Service Mesh solutions
