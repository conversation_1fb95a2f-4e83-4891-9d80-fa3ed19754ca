# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out
_tmp/
_output/

# Dependency directories (remove the comment below to include it)
# vendor/

.idea/
.vscode/

# OSX trash
.DS_Store

# sub chart tgz
charts/karmada/charts
charts/karmada-operator/charts

tmp
cmd/cluster-config