package options

import (
	"net"

	"k8s.io/apimachinery/pkg/util/validation/field"
)

// Validate checks Options and return a slice of found errs.
func (o *Options) Validate() field.ErrorList {
	errs := field.ErrorList{}
	rootPath := field.NewPath("Options")

	if net.ParseIP(o.BindAddress) == nil {
		errs = append(errs, field.Invalid(rootPath.Child("BindAddress"), o<PERSON><PERSON>d<PERSON>dd<PERSON>, "not a valid textual representation of an IP address"))
	}

	if o.SecurePort < 0 || o.SecurePort > 65535 {
		errs = append(errs, field.Invalid(rootPath.Child("SecurePort"), o.SecurePort, "must be a valid port between 0 and 65535 inclusive"))
	}

	return errs
}
