package options

import (
	"github.com/spf13/pflag"
)

const (
	defaultBindAddress = "0.0.0.0"
	defaultPort        = 8445
	defaultCertDir     = "/tmp/k8s-webhook-server/serving-certs"
)

// Options contains everything necessary to create and run webhook server.
type Options struct {
	BindAddress string
	SecurePort  int
	CertDir     string
}

// NewOptions builds an empty options.
func NewOptions() *Options {
	return &Options{}
}

// AddFlags adds flags to the specified FlagSet.
func (o *Options) AddFlags(flags *pflag.FlagSet) {
	flags.StringVar(&o.BindAddress, "bind-address", defaultBindAddress, "The IP address on which to listen for the --secure-port port.")
	flags.IntVar(&o.SecurePort, "secure-port", defaultPort, "The secure port on which to serve HTTPS.")
	flags.StringVar(&o.CertDir, "cert-dir", defaultCertDir, "The directory that contains the server key(named tls.key) and certificate(named tls.crt).")
}
